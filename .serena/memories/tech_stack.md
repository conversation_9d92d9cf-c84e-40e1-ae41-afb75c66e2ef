# 技术栈和依赖

## 核心技术栈
- **Python**: 3.9+ (主要编程语言)
- **Freqtrade**: 2024.1+ (交易机器人框架)
- **SQLAlchemy**: 2.0.0+ (ORM和数据库连接)
- **Pandas**: 2.1.0+ (数据分析和处理)
- **NumPy**: 1.24.0+ (数值计算)

## 交易相关
- **CCXT**: 4.1.0+ (加密货币交易所连接库)
- **python-binance**: 1.0.19+ (币安交易所专用API)

## 数据库
- **MySQL**: 通过PyMySQL连接
- **Redis**: 5.0.0+ (缓存和会话存储)
- **SQLite**: 默认支持，用于开发和测试

## 分析和可视化
- **Plotly**: 5.15.0+ (交互式图表)
- **Matplotlib**: 3.7.0+ (基础绘图)
- **Seaborn**: 0.12.0+ (统计可视化)
- **scikit-learn**: 1.3.0+ (机器学习)
- **TA-Lib**: 0.4.25+ (技术指标分析)

## 通知和API
- **python-telegram-bot**: 20.0+ (Telegram机器人)
- **requests**: 2.31.0+ (HTTP请求)
- **wechatpy**: 1.8.0+ (企业微信通知)

## 配置和开发工具
- **pydantic**: 2.0.0+ (数据验证和设置管理)
- **python-dotenv**: 1.0.0+ (环境变量管理)
- **loguru**: 0.7.0+ (日志记录)
- **rich**: 13.0.0+ (终端美化)
- **typer**: 0.9.0+ (CLI命令工具)

## 开发工具
- **pytest**: 7.4.0+ (测试框架)
- **black**: 23.0.0+ (代码格式化)
- **flake8**: 6.0.0+ (代码检查)
- **mypy**: 1.5.0+ (类型检查)