# 代码风格和约定

## 代码格式化
- **Black**: 使用Black作为代码格式化工具
  - 行长度: 88字符
  - 目标Python版本: 3.9+
  - 包含文件: `.py`和`.pyi`文件

## 类型检查
- **MyPy**: 使用MyPy进行静态类型检查
  - Python版本: 3.9
  - `warn_return_any = true`
  - `warn_unused_configs = true`
  - `disallow_untyped_defs = true`

## 代码检查
- **Flake8**: 使用Flake8进行代码质量检查

## 命名约定
- **类名**: 使用PascalCase (如 `ArbitrageCalculator`, `Model3Strategy`)
- **函数/方法名**: 使用snake_case (如 `calculate_classic_arbitrage`)
- **常量**: 使用UPPER_SNAKE_CASE (如 `INTERFACE_VERSION`)
- **私有方法**: 以下划线开头 (如 `_get_futures_price`)

## 模块结构
- **配置**: `config/` 目录存放配置相关代码
- **策略**: `strategies/` 目录存放交易策略
- **服务**: `services/` 目录存放业务逻辑服务
- **模型**: `models/` 目录存放数据模型
- **工具**: `utils/` 目录存放工具函数

## 文档约定
- 使用中文注释和文档字符串
- 类和重要方法都需要文档字符串
- 复杂算法需要详细注释说明

## 导入规范
- 使用相对导入在包内部
- 按标准库、第三方库、本地库的顺序组织导入
- 在`__init__.py`中定义`__all__`列表