# 任务完成检查清单

## 代码开发完成后需要执行的操作

### 1. 代码质量检查
```bash
# 代码格式化
black src/ tests/

# 代码风格检查  
flake8 src/ tests/

# 类型检查
mypy src/
```

### 2. 测试验证
```bash
# 运行所有测试
pytest -v

# 运行特定模块测试
pytest tests/test_basic.py -v

# 检查测试覆盖率（如果配置了coverage）
coverage run -m pytest
coverage report
```

### 3. 功能验证
```bash
# 验证配置加载
python -m optimus.cli config-info

# 测试数据库连接
python scripts/test_database.py

# 测试通知服务
python -m optimus.cli test-notification
```

### 4. 集成测试
```bash
# 下载测试数据
python -m optimus.cli download-data --pairs "BTC/USDT" --days 7

# 运行回测验证
python -m optimus.cli backtest --strategy Model3Strategy --timerange 20241201-20241225

# 分析套利机会
python -m optimus.cli analyze-arbitrage --symbol "BTC/USDT" --spot-price 95000 --futures-price 95100
```

### 5. 部署前检查
```bash
# 检查依赖完整性
pip check

# 验证项目安装
pip install -e .

# 验证命令行工具
optimus config-info
```

### 6. 文档更新
- 更新README.md（如果有重大变更）
- 更新相关文档（如果有新功能）
- 添加或更新代码注释和文档字符串

### 7. 版本控制
```bash
# 检查git状态
git status

# 添加更改
git add .

# 提交更改
git commit -m "描述性提交信息"

# 推送到远程（如果需要）
git push
```

### 8. 生产环境注意事项
- 确认`.env`文件配置正确
- 验证API密钥和权限
- 确认数据库连接稳定
- 检查通知服务配置
- 确认交易模式设置（dry_run状态）

### 9. 性能检查
- 验证内存使用情况
- 检查数据库查询性能
- 监控API调用频率
- 检查日志输出正常