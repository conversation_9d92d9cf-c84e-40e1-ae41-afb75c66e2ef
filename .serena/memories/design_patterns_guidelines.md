# 设计模式和开发指南

## 架构模式

### 分层架构
项目采用经典的分层架构模式：
- **表示层**: CLI命令接口 (`cli.py`)
- **业务逻辑层**: 服务类 (`services/`)
- **数据访问层**: 模型类 (`models/`)
- **配置层**: 配置管理 (`config/`)

### 策略模式
交易策略使用策略模式：
- **基础策略类**: `BaseArbitrageStrategy`
- **具体策略**: `Model3Strategy`, `Model4Strategy`
- 便于扩展新的交易策略

## 设计原则

### 1. 单一职责原则
- 每个类和模块都有明确的单一职责
- `ArbitrageCalculator`: 专门负责套利计算
- `NotificationService`: 专门负责通知发送
- `BacktestAnalyzer`: 专门负责回测分析

### 2. 依赖注入
- 使用Pydantic的Settings进行配置管理
- 通过`get_settings()`函数获取全局配置
- 避免硬编码配置值

### 3. 错误处理
- 使用异常处理机制
- 自定义业务异常类
- 提供有意义的错误信息

### 4. 数据验证
- 使用Pydantic进行数据验证
- 在`validators.py`中定义验证函数
- 确保数据类型和范围正确性

## 编程约定

### 1. 配置管理
```python
from optimus.config import get_settings
settings = get_settings()
```

### 2. 日志记录
```python
from loguru import logger
logger.info("业务操作信息")
logger.error("错误信息")
```

### 3. 数据库操作
```python
from optimus.config.database_config import SessionLocal
with SessionLocal() as db:
    # 数据库操作
    pass
```

### 4. 异步处理
- 使用async/await进行异步操作
- 特别是网络请求和I/O操作

### 5. 类型提示
- 所有函数都应该有类型提示
- 使用Union, Optional等类型
- 复杂类型使用TypeVar或Generic

## 特定指南

### 交易策略开发
1. 继承`BaseArbitrageStrategy`
2. 实现必要的抽象方法
3. 使用统一的数据格式
4. 包含完整的文档说明

### 服务类开发
1. 使用依赖注入获取配置
2. 提供清晰的公共接口
3. 内部方法使用下划线前缀
4. 包含适当的错误处理

### 数据模型设计
1. 继承SQLAlchemy的Base类
2. 使用类型注解
3. 定义合适的索引
4. 包含创建和更新时间字段

### 测试编写
1. 每个功能模块都有对应测试
2. 使用mock进行外部依赖隔离
3. 测试边界条件和异常情况
4. 保持测试的独立性