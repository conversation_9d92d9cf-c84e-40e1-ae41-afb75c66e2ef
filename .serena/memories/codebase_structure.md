# 代码库结构

## 顶级目录结构
```
optimus-python/
├── config/              # 配置文件
├── docs/                # 项目文档
├── scripts/             # 辅助脚本
├── src/optimus/         # 主要源代码
├── tests/               # 测试代码
├── user_data/           # 用户数据和策略
├── requirements.txt     # Python依赖
├── pyproject.toml       # 项目配置
└── README.md           # 项目说明
```

## src/optimus/ 核心模块

### 配置模块 (config/)
- `settings.py`: 主要配置类和设置管理
- `database_config.py`: 数据库连接配置
- `exchange_config.py`: 交易所配置

### 数据模型 (models/)
- `base.py`: 基础数据模型
- `investment.py`: 投资计划和记录模型
- `market_data.py`: 市场数据模型（K线、资金费率等）

### 业务服务 (services/)
- `arbitrage_calculator.py`: 套利计算服务
- `notification_service.py`: 通知服务（微信、Telegram）

### 交易策略 (strategies/)
- `base_strategy.py`: 基础策略类
- `model3_strategy.py`: Model3经典套利策略

### 回测分析 (backtest/)
- `analyzer.py`: 回测结果分析器

### 工具函数 (utils/)
- `helpers.py`: 通用帮助函数
- `validators.py`: 数据验证函数

### 主要入口
- `main.py`: 应用主入口
- `cli.py`: 命令行接口

## 配置文件 (config/)
- `config.json`: 主要Freqtrade配置
- `config_backtest.json`: 回测专用配置
- `config_template.json`: 配置模板

## 测试结构 (tests/)
- `test_basic.py`: 基础功能测试
- `unit/`: 单元测试
- `integration/`: 集成测试

## 用户数据 (user_data/)
- `data/`: 历史数据存储
- `logs/`: 日志文件
- `notebooks/`: Jupyter笔记本
- `strategies/`: 用户自定义策略

## 关键文件
- `requirements.txt`: 生产依赖
- `pyproject.toml`: 项目元数据和开发依赖
- `setup.py`: 包安装配置
- `docker-compose.yml`: Docker服务配置