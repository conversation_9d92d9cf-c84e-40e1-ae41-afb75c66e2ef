# 建议的开发命令

## 项目运行命令

### 快速启动
```bash
./scripts/quick_start.sh               # 交互式快速启动脚本
```

### 主要功能命令
```bash
python -m optimus.cli config-info          # 查看配置信息
python -m optimus.cli analyze-arbitrage    # 分析套利机会
python -m optimus.cli download-data        # 下载历史数据
python -m optimus.cli backtest             # 运行策略回测
python -m optimus.cli run                  # 启动交易系统
python -m optimus.cli test-notification    # 测试通知服务
```

### Freqtrade原生命令
```bash
freqtrade trade --config config/config.json                    # 启动交易
freqtrade backtesting --config config/config_backtest.json     # 运行回测
freqtrade download-data --pairs "BTC/USDT,ETH/USDT" --days 30  # 下载数据
```

## 开发和测试命令

### 包管理
```bash
pip install -r requirements.txt    # 安装依赖
pip install -e .                  # 开发模式安装
```

### 测试命令
```bash
pytest                           # 运行所有测试
pytest tests/test_basic.py       # 运行特定测试文件
pytest -v                       # 详细输出测试结果
pytest tests/unit/              # 运行单元测试
pytest tests/integration/       # 运行集成测试
```

### 代码质量检查
```bash
black src/ tests/               # 格式化代码
flake8 src/ tests/             # 代码风格检查
mypy src/                      # 类型检查
```

### 数据库相关
```bash
python scripts/test_database.py   # 测试数据库连接
python -m optimus.main           # 初始化数据库
```

## 系统命令（macOS）

### 基础命令
```bash
ls -la                         # 列出文件详情
cd <directory>                 # 切换目录
pwd                           # 显示当前目录
grep -r "pattern" .           # 递归搜索文件内容
find . -name "*.py"           # 查找Python文件
```

### Git命令
```bash
git status                    # 查看状态
git add .                     # 添加所有更改
git commit -m "message"       # 提交更改
git push                      # 推送到远程
git pull                      # 拉取远程更改
```

### 环境管理
```bash
python -m venv venv          # 创建虚拟环境
source venv/bin/activate     # 激活虚拟环境
deactivate                   # 退出虚拟环境
```

## Docker命令（如果使用）
```bash
docker-compose up -d         # 启动Docker服务
docker-compose down          # 停止Docker服务
docker-compose logs          # 查看日志
```