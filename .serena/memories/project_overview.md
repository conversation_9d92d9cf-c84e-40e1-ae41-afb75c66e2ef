# Optimus Python 项目概述

## 项目目的
Optimus Python 是一个基于 Freqtrade 框架的专业加密货币量化交易系统，专门用于币安交易所的套利交易。该项目是从原Java版本迁移而来的加密货币套利交易系统。

## 核心功能
- **多策略套利**: 实现Model3经典套利、Model4对冲套利等多种策略
- **智能风控**: 自动止盈止损、提前平仓、风险管理
- **实时监控**: 支持企业微信、Telegram等多种通知方式
- **专业回测**: 基于Freqtrade的强大回测引擎，支持套利策略专用分析
- **数据管理**: 完整的历史数据下载和管理功能

## 支持的交易对
- BTC/USDT, ETH/USDT, XRP/USDT
- ADA/USDT, LINK/USDT, LTC/USDT  
- DOT/USDT, BNB/USDT, BCH/USDT, SOL/USDT

## 技术架构
- 基于Freqtrade框架
- 支持现货-期货套利
- 使用币安交易所API
- 支持实时数据获取和回测

## 项目模式
- 生产模式: 连接实际交易所进行交易
- 模拟模式: 使用虚拟资金进行测试
- 回测模式: 基于历史数据测试策略性能