# 项目结构说明

## 📁 优化后的项目目录结构

```
optimus-python/
├── 📄 README.md                     # 项目主文档
├── 📄 project.config.json           # 项目配置文件
├── 📄 .gitignore                    # Git忽略文件
│
├── 📁 config/                       # 配置文件目录
│   ├── config_template.json         # 配置模板
│   ├── config_backtest.json         # 回测配置
│   ├── config_backtest_with_proxy.json  # 带代理的回测配置
│   └── config_backtest_no_proxy.json    # 不带代理的回测配置
│
├── 📁 user_data/                    # Freqtrade用户数据
│   ├── strategies/                  # 交易策略目录
│   │   └── simple_model3_strategy.py
│   ├── data/                       # 历史数据
│   ├── logs/                       # 日志文件
│   ├── backtest_results/           # 回测结果
│   ├── hyperopt_results/           # 超参数优化结果
│   └── notebooks/                  # Jupyter笔记本
│
├── 📁 web-dashboard/               # Web管理界面
│   ├── 📁 backend/                 # FastAPI后端
│   │   ├── 📁 app/                 # 应用核心
│   │   │   ├── 📁 api/             # API路由
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth.py         # 认证API
│   │   │   │   ├── backtests.py    # 回测API
│   │   │   │   ├── dashboard.py    # 仪表板API
│   │   │   │   ├── health.py       # 健康检查API
│   │   │   │   ├── monitoring.py   # 监控API
│   │   │   │   ├── settings.py     # 设置API
│   │   │   │   ├── strategies.py   # 策略API
│   │   │   │   ├── trading.py      # 交易API
│   │   │   │   └── websocket_router.py  # WebSocket路由
│   │   │   ├── 📁 core/            # 核心模块
│   │   │   │   ├── __init__.py
│   │   │   │   ├── config.py       # 配置管理
│   │   │   │   ├── database.py     # 数据库配置
│   │   │   │   ├── dependencies.py # 依赖注入
│   │   │   │   └── strategy_registry.py  # 策略注册器
│   │   │   ├── 📁 schemas/         # 数据模型
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth.py         # 认证模型
│   │   │   │   ├── backtest.py     # 回测模型
│   │   │   │   ├── dashboard.py    # 仪表板模型
│   │   │   │   ├── monitoring.py   # 监控模型
│   │   │   │   ├── settings.py     # 设置模型
│   │   │   │   ├── strategy.py     # 策略模型
│   │   │   │   └── trading.py      # 交易模型
│   │   │   └── 📁 services/        # 业务服务
│   │   │       ├── __init__.py
│   │   │       ├── auth_service.py # 认证服务
│   │   │       ├── backtest_service.py  # 回测服务
│   │   │       ├── dashboard_service.py # 仪表板服务
│   │   │       ├── monitoring_service.py # 监控服务
│   │   │       ├── settings_service.py  # 设置服务
│   │   │       ├── strategy_service.py  # 策略服务
│   │   │       └── trading_service.py   # 交易服务
│   │   ├── 📁 backtest_results/    # 回测结果存储
│   │   ├── 📁 logs/               # 后端日志
│   │   ├── 📁 user_data/          # 后端用户数据
│   │   │   └── strategies/        # 策略文件链接
│   │   ├── 📄 main.py             # 应用入口
│   │   ├── 📄 run.py              # 启动脚本
│   │   ├── 📄 requirements.txt    # Python依赖
│   │   └── 📄 start_backend.sh    # 后端启动脚本
│   │
│   └── 📁 frontend/               # React前端
│       ├── 📁 public/             # 静态资源
│       ├── 📁 src/                # 源代码
│       │   ├── 📁 components/     # 可复用组件
│       │   ├── 📁 pages/          # 页面组件
│       │   ├── 📁 services/       # API服务
│       │   ├── 📁 utils/          # 工具函数
│       │   ├── App.tsx            # 主应用组件
│       │   └── index.tsx          # 入口文件
│       ├── 📄 package.json        # Node.js依赖
│       ├── 📄 tsconfig.json       # TypeScript配置
│       └── 📄 start_frontend.sh   # 前端启动脚本
│
├── 📁 scripts/                    # 工具脚本
│   ├── start_project.sh           # 项目启动脚本
│   ├── cleanup_project.py         # 项目清理脚本
│   ├── install.sh                 # 安装脚本
│   ├── quick_start.sh             # 快速启动脚本
│   ├── test_database.py           # 数据库测试
│   └── update_config.py           # 配置更新脚本
│
└── 📁 venv_freqtrade/             # Python虚拟环境
    ├── bin/                       # 可执行文件
    ├── lib/                       # 库文件
    └── pyvenv.cfg                 # 环境配置
```

## 🗑️ 已删除的无用目录和文件

### 删除的目录：
- `src/` - 旧的Python包结构
- `tests/` - 基本测试文件
- `docs/` - 旧文档目录
- `backtest_results/` - 根目录重复的回测结果
- `web-dashboard/config/` - 重复的配置目录
- `web-dashboard/user_data/` - 重复的用户数据目录
- `web-dashboard/shared/` - 空目录
- `web-dashboard/docker/` - 空目录
- `web-dashboard/docs/` - 空目录

### 删除的文件：
- `Dockerfile` - 未使用的Docker配置
- `docker-compose*.yml` - 重复的Docker配置
- `pyproject.toml` - 旧的Python项目配置
- `setup.py` - 旧的安装脚本
- `requirements.txt` - 根目录重复的依赖文件
- `web-dashboard/start_development.sh` - 旧的启动脚本
- `web-dashboard/README.md` - 重复的文档
- 各种测试文件和临时脚本

## 🎯 核心功能模块

### 后端 (FastAPI)
- **API路由**: 提供RESTful API和WebSocket接口
- **核心模块**: 配置管理、数据库、策略注册
- **业务服务**: 回测、策略、交易、监控等服务
- **数据模型**: Pydantic模型定义

### 前端 (React + TypeScript)
- **组件化设计**: 可复用的UI组件
- **页面路由**: 仪表板、策略、回测、交易等页面
- **API服务**: 与后端API的通信层
- **工具函数**: 通用工具和辅助函数

### 配置管理
- **模板配置**: 可复制的配置模板
- **环境配置**: 开发、测试、生产环境配置
- **代理配置**: 支持网络代理的配置

### 策略系统
- **策略注册**: 自动发现和注册策略
- **策略管理**: 策略信息和参数管理
- **回测引擎**: 完整的策略回测功能

## 🚀 启动方式

### 推荐方式（统一启动）
```bash
./scripts/start_project.sh start
```

### 独立启动
```bash
# 后端
cd web-dashboard/backend
./start_backend.sh

# 前端
cd web-dashboard/frontend  
./start_frontend.sh
```

## 📊 访问地址

- 🌐 前端界面: http://localhost:3000
- 🔧 后端API: http://localhost:8081
- 📚 API文档: http://localhost:8081/api/docs
