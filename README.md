# Freqtrade Web Dashboard

一个基于Freqtrade的量化交易系统，提供现代化的Web管理界面，支持策略管理、回测分析、实时监控等功能。

## ✨ 功能特性

- 🤖 **策略管理**: 可视化策略配置和管理
- 📊 **回测分析**: 完整的策略回测功能，支持多种指标分析
- 📈 **实时监控**: 实时交易数据和系统状态监控
- 🎯 **交易管理**: 交易记录查看和管理
- 🔔 **告警系统**: 系统状态和交易告警
- 📱 **响应式设计**: 支持桌面和移动端访问

## 🏗️ 项目架构

```
optimus-python/
├── 📁 config/                    # 配置文件
│   ├── config_backtest.json      # 回测配置
│   ├── config_backtest_with_proxy.json  # 带代理的回测配置
│   └── config_template.json      # 配置模板
├── 📁 user_data/                 # 用户数据
│   ├── strategies/               # 交易策略
│   ├── data/                    # 历史数据
│   └── logs/                    # 日志文件
├── 📁 web-dashboard/             # Web管理界面
│   ├── 📁 frontend/             # React + TypeScript 前端
│   │   ├── src/
│   │   │   ├── components/      # 可复用组件
│   │   │   ├── pages/          # 页面组件
│   │   │   ├── services/       # API服务
│   │   │   └── utils/          # 工具函数
│   │   └── package.json
│   └── 📁 backend/              # FastAPI 后端
│       ├── app/
│       │   ├── api/            # API路由
│       │   ├── core/           # 核心模块
│       │   ├── services/       # 业务服务
│       │   ├── schemas/        # 数据模型
│       │   └── database.py     # 数据库配置
│       ├── main.py             # 应用入口
│       ├── run.py              # 启动脚本
│       └── requirements.txt    # Python依赖
├── 📁 scripts/                  # 工具脚本
│   ├── start_project.sh        # 项目启动脚本
│   └── cleanup_project.py      # 项目清理脚本
└── 📁 venv_freqtrade/          # Python虚拟环境
```

## 🚀 快速开始

### 1. 环境准备

**系统要求:**
- Python 3.8+
- Node.js 16+
- MySQL 8.0+ (可选，默认使用SQLite)

**安装依赖:**

```bash
# 1. 克隆项目
git clone <repository-url>
cd optimus-python

# 2. 创建Python虚拟环境
python -m venv venv_freqtrade
source venv_freqtrade/bin/activate  # Linux/Mac
# 或 venv_freqtrade\Scripts\activate  # Windows

# 3. 安装Freqtrade和后端依赖
pip install freqtrade
cd web-dashboard/backend
pip install -r requirements.txt
cd ../..

# 4. 安装前端依赖
cd web-dashboard/frontend
npm install
cd ../..
```

### 2. 配置设置

```bash
# 复制配置模板
cp config/config_template.json config/config_backtest.json

# 编辑配置文件（可选，默认配置可直接使用）
vim config/config_backtest.json
```

### 3. 启动项目

**方式一：使用启动脚本（推荐）**

```bash
# 启动完整项目（前端+后端）
./scripts/start_project.sh start

# 查看服务状态
./scripts/start_project.sh status

# 停止服务
./scripts/start_project.sh stop

# 重启服务
./scripts/start_project.sh restart
```

**方式二：手动启动**

```bash
# 启动后端服务
cd web-dashboard/backend
./start_backend.sh
# 或者: ../../venv_freqtrade/bin/python run.py

# 启动前端服务（新终端）
cd web-dashboard/frontend
npm start
```

### 4. 访问应用

- 🌐 **前端界面**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8081
- 📚 **API文档**: http://localhost:8081/api/docs

## 📖 使用指南

### 策略管理

1. **查看策略**: 访问策略页面查看已注册的交易策略
2. **策略信息**: 查看策略详细参数和配置
3. **添加策略**: 在 `user_data/strategies/` 目录添加新策略文件

### 回测功能

1. **创建回测**: 选择策略、设置时间范围和参数
2. **监控进度**: 实时查看回测执行状态
3. **查看结果**: 分析回测报告和交易记录

### 实时监控

1. **仪表板**: 查看系统概览和关键指标
2. **交易记录**: 查看历史交易和实时状态
3. **系统监控**: 查看系统资源使用情况

## 🛠️ 开发指南

### 添加新策略

1. 在 `user_data/strategies/` 创建策略文件
2. 使用策略注册机制：

```python
from app.core.strategy_registry import register_strategy
from freqtrade.strategy import IStrategy

class MyStrategy(IStrategy):
    # 策略实现
    pass

# 注册策略
register_strategy(
    name="MyStrategy",
    description="我的交易策略",
    author="Your Name",
    version="1.0.0",
    category="technical",
    timeframes=["5m", "15m", "1h"],
    pairs=["BTC/USDT", "ETH/USDT"],
    parameters={
        "param1": {"type": "int", "default": 10, "description": "参数1"}
    }
)
```

### 前端开发

```bash
cd web-dashboard/frontend

# 开发模式
npm start

# 构建生产版本
npm run build

# 代码检查
npm run lint
```

### 后端开发

```bash
cd web-dashboard/backend

# 开发模式（自动重载）
../../venv_freqtrade/bin/python run.py --debug

# 生产模式
../../venv_freqtrade/bin/python run.py
```

## 🔧 配置说明

### 代理配置

如果需要使用代理访问交易所API，使用带代理的配置：

```json
{
  "ccxt_config": {
    "enableRateLimit": true,
    "rateLimit": 5000,
    "proxies": {
      "http": "http://127.0.0.1:7897",
      "https": "http://127.0.0.1:7897"
    }
  }
}
```

### 数据库配置

默认使用SQLite，如需使用MySQL：

```python
# web-dashboard/backend/app/database.py
DATABASE_URL = "mysql+aiomysql://user:password@localhost/freqtrade_dashboard"
```

## 🧹 项目维护

### 清理项目

```bash
# 清理无用文件和缓存
python scripts/cleanup_project.py
```

### 日志管理

- 后端日志: `web-dashboard/backend/logs/backend.log`
- 回测日志: `web-dashboard/backend/backtest_results/`

## 🐛 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python虚拟环境是否激活
   - 确认所有依赖已安装：`pip install -r requirements.txt`

2. **前端无法连接后端**
   - 确认后端服务运行在8081端口
   - 检查防火墙设置

3. **回测进程卡住**
   - 系统会自动检测并处理卡住的进程
   - 手动终止：查看进程ID并kill

4. **策略不显示**
   - 确保策略文件包含注册代码
   - 检查策略文件语法错误

### 获取帮助

- 查看API文档: http://localhost:8081/api/docs
- 检查日志文件获取详细错误信息
- 使用 `./scripts/start_project.sh status` 检查服务状态

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！
