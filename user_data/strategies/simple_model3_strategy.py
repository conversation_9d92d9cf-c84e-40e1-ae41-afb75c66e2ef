"""
Simple Model3 Strategy - Freqtrade策略文件

这是一个用于Freqtrade的策略文件，实现了一个简单的移动平均线交叉策略
"""

import logging
import numpy as np
import pandas as pd
import pandas_ta as ta
from pandas import DataFrame
from freqtrade.strategy import IStrategy, IntParameter

logger = logging.getLogger(__name__)

class SimpleModel3Strategy(IStrategy):
    """
    简单的移动平均线交叉策略
    """
    # 策略参数
    minimal_roi = {
        "0": 0.01,
        "30": 0.005,
        "60": 0.0025
    }
    
    stoploss = -0.05
    timeframe = '5m'
    
    # 优化参数
    buy_ema_short = IntParameter(5, 15, default=10, space="buy")
    buy_ema_long = IntParameter(20, 50, default=30, space="buy")
    
    # 处理指标
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算技术指标
        """
        # 计算EMA
        dataframe['ema_short'] = ta.ema(dataframe['close'], length=10)
        dataframe['ema_long'] = ta.ema(dataframe['close'], length=30)
        
        # 添加RSI指标
        dataframe['rsi'] = ta.rsi(dataframe['close'], length=14)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入信号
        """
        dataframe.loc[
            (
                # 短期EMA上穿长期EMA
                (dataframe['ema_short'] > dataframe['ema_long']) &
                (dataframe['ema_short'].shift(1) <= dataframe['ema_long'].shift(1)) &
                (dataframe['rsi'] > 30) &  # RSI不在超卖区
                (dataframe['volume'] > 0)  # 确保有成交量
            ),
            'enter_long'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成卖出信号
        """
        dataframe.loc[
            (
                # 短期EMA下穿长期EMA
                (dataframe['ema_short'] < dataframe['ema_long']) &
                (dataframe['ema_short'].shift(1) >= dataframe['ema_long'].shift(1)) &
                (dataframe['rsi'] > 70) &  # RSI在超买区
                (dataframe['volume'] > 0)  # 确保有成交量
            ),
            'exit_long'] = 1
        
        return dataframe

# 导出给Freqtrade使用
__all__ = ["SimpleModel3Strategy"] 