# 币安 API 配置
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
BINANCE_TESTNET_API_KEY=your_testnet_api_key
BINANCE_TESTNET_SECRET_KEY=your_testnet_secret_key

# 代理配置
PROXY_ENABLED=false
PROXY_HOST=127.0.0.1
PROXY_PORT=7890

# 数据库配置 - 使用Docker部署的MySQL
DATABASE_URL=mysql+pymysql://root:123456@localhost:3306/coindb
REDIS_URL=redis://:redis123@localhost:6379/0

# 企业微信通知
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# 交易配置
MIN_VALID_AMOUNT=100.0
MAX_LEVERAGE=10
DEFAULT_LEVERAGE=5
RISK_LEVEL=medium

# 环境配置
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true
