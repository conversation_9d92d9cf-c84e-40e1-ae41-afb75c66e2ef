#!/bin/bash

# Freqtrade Web Dashboard Frontend 启动脚本

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🎨 启动Freqtrade Web Dashboard Frontend..."
echo "工作目录: $SCRIPT_DIR"

# 切换到前端目录
cd "$SCRIPT_DIR"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js 未安装"
    echo "请先安装Node.js: https://nodejs.org/"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm 未安装"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 设置环境变量
export REACT_APP_API_BASE_URL=http://localhost:8081/api/v1

# 启动前端开发服务器
echo "🚀 启动前端开发服务器..."
echo "前端地址: http://localhost:3000"
echo "API地址: http://localhost:8081"

exec npm start
