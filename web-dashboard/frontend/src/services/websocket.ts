/**
 * WebSocket 实时数据服务
 * 提供与后端的实时数据通信
 */

export interface WebSocketMessage {
  type: string;
  topic: string;
  data: any;
  timestamp: string;
}

export interface DashboardData {
  total_balance: number;
  open_trades: number;
  closed_trades: number;
  total_profit: number;
  win_rate: number;
  profit_factor: number;
  recent_trades: any[];
  profit_trend: Array<{ time: string; profit: number }>;
}

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export type WebSocketSubscription = {
  topic: string;
  callback: (data: any) => void;
};

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private subscriptions: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private statusCallbacks: Set<(status: ConnectionStatus) => void> = new Set();
  private messageQueue: WebSocketMessage[] = [];
  private heartbeatInterval: number | null = null;

  constructor(url: string = 'ws://localhost:8081/api/v1/ws') {
    this.url = url;
  }

  /**
   * 连接到WebSocket服务器
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.setConnectionStatus('connecting');
      
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('🔗 WebSocket 连接已建立');
          this.setConnectionStatus('connected');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.processMessageQueue();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('❌ 解析WebSocket消息失败:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('🔌 WebSocket 连接已关闭:', event.code, event.reason);
          this.setConnectionStatus('disconnected');
          this.stopHeartbeat();
          
          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('❌ WebSocket 连接错误:', error);
          this.setConnectionStatus('error');
          reject(error);
        };

      } catch (error) {
        console.error('❌ 创建WebSocket连接失败:', error);
        this.setConnectionStatus('error');
        reject(error);
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, '主动断开连接');
      this.ws = null;
    }
    this.stopHeartbeat();
    this.setConnectionStatus('disconnected');
  }

  /**
   * 订阅特定主题的消息
   */
  subscribe(topic: string, callback: (data: any) => void): () => void {
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, new Set());
    }
    
    this.subscriptions.get(topic)!.add(callback);

    // 发送订阅请求
    this.sendMessage({
      type: 'subscribe',
      topic,
      data: {},
      timestamp: new Date().toISOString()
    });

    // 返回取消订阅函数
    return () => {
      const callbacks = this.subscriptions.get(topic);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.subscriptions.delete(topic);
          // 发送取消订阅请求
          this.sendMessage({
            type: 'unsubscribe',
            topic,
            data: {},
            timestamp: new Date().toISOString()
          });
        }
      }
    };
  }

  /**
   * 取消订阅主题或特定回调
   */
  unsubscribe(topic: string, callback?: (data: any) => void): void {
    if (callback) {
      // 取消特定回调
      const callbacks = this.subscriptions.get(topic);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.subscriptions.delete(topic);
          this.sendMessage({
            type: 'unsubscribe',
            topic,
            data: {},
            timestamp: new Date().toISOString()
          });
        }
      }
    } else {
      // 取消整个主题
      this.subscriptions.delete(topic);
      this.sendMessage({
        type: 'unsubscribe',
        topic,
        data: {},
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 发送消息到服务器
   */
  private sendMessage(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // 如果连接未就绪，将消息加入队列
      this.messageQueue.push(message);
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    const callbacks = this.subscriptions.get(message.topic);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(message.data);
        } catch (error) {
          console.error(`❌ 处理${message.topic}消息回调失败:`, error);
        }
      });
    }
  }

  /**
   * 设置连接状态
   */
  private setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
    this.statusCallbacks.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('❌ 连接状态回调失败:', error);
      }
    });
  }

  /**
   * 监听连接状态变化
   */
  onConnectionStatusChange(callback: (status: ConnectionStatus) => void): () => void {
    this.statusCallbacks.add(callback);
    // 立即回调当前状态
    callback(this.connectionStatus);
    
    return () => {
      this.statusCallbacks.delete(callback);
    };
  }

  /**
   * 获取当前连接状态
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`🔄 将在 ${delay}ms 后尝试第 ${this.reconnectAttempts} 次重连...`);
    
    setTimeout(() => {
      if (this.connectionStatus !== 'connected') {
        this.connect().catch(error => {
          console.error('❌ 重连失败:', error);
        });
      }
    }, delay);
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.sendMessage({
          type: 'ping',
          topic: 'heartbeat',
          data: {},
          timestamp: new Date().toISOString()
        });
      }
    }, 30000); // 每30秒发送一次心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      window.clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.sendMessage(message);
      }
    }
  }

  /**
   * 获取是否已连接
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * 发送ping消息
   */
  ping(): void {
    this.sendMessage({
      type: 'ping',
      topic: 'heartbeat',
      data: {},
      timestamp: new Date().toISOString()
    });
  }
}

// 创建全局WebSocket服务实例
export const websocketService = new WebSocketService();

// 导出默认实例
export default websocketService; 