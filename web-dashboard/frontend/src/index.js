import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import axios from 'axios';

// 配置axios默认设置
axios.defaults.baseURL = 'http://localhost:8081'; // 后端服务地址
axios.defaults.timeout = 30000; // 30秒超时
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';

// 添加请求拦截器
axios.interceptors.request.use(
  config => {
    console.log(`🌐 发送请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  error => {
    console.error('❌ 请求错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
axios.interceptors.response.use(
  response => {
    console.log(`✅ 响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`);
    return response;
  },
  error => {
    console.error('❌ 响应错误:', error);
    
    // 特殊处理404错误
    if (error.response && error.response.status === 404) {
      console.warn('⚠️ 资源未找到，请检查API路径是否正确');
    }
    
    // 特殊处理网络错误
    if (error.message === 'Network Error') {
      console.warn('⚠️ 网络错误，请检查后端服务是否运行');
    }
    
    return Promise.reject(error);
  }
);

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
); 