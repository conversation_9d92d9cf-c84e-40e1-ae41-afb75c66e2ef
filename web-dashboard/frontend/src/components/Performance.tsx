import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Spin, Alert, DatePicker, Select, Table } from 'antd';
import { Line, Column, Pie } from '@ant-design/plots';
import { TrophyOutlined, DollarOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface PerformanceData {
  daily_profit: Array<{
    date: string;
    profit: number;
    profit_abs: number;
  }>;
  monthly_stats: {
    total_trades: number;
    profitable_trades: number;
    total_profit: number;
    win_rate: number;
    profit_factor: number;
  };
  performance_by_pair: Array<{
    pair: string;
    count: number;
    profit: number;
    profit_abs: number;
  }>;
}

interface PairPerformance {
  pair: string;
  count: number;
  profit: number;
  profit_abs: number;
  win_rate?: number;
}

const Performance: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<PerformanceData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<string>('30');
  const [chartType, setChartType] = useState<string>('daily');

  // 获取性能数据
  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/v1/dashboard/performance');
      if (!response.ok) {
        throw new Error('获取性能数据失败');
      }

      const performanceData = await response.json();
      setData(performanceData);

    } catch (err) {
      console.error('获取性能数据失败:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
    
    // 设置定时刷新
    const interval = setInterval(fetchPerformanceData, 30000); // 每30秒刷新
    
    return () => clearInterval(interval);
  }, [timeRange]);

  // 准备每日收益图表数据
  const prepareDailyProfitData = () => {
    if (!data?.daily_profit) return [];
    
    return data.daily_profit.map(item => ({
      date: item.date,
      profit: item.profit_abs || item.profit || 0,
      type: (item.profit_abs || item.profit || 0) >= 0 ? '盈利' : '亏损'
    }));
  };

  // 准备交易对性能数据
  const preparePairPerformanceData = () => {
    if (!data?.performance_by_pair) return [];
    
    return data.performance_by_pair
      .sort((a, b) => Math.abs(b.profit_abs || b.profit) - Math.abs(a.profit_abs || a.profit))
      .slice(0, 10) // 只显示前10个
      .map(item => ({
        pair: item.pair,
        profit: item.profit_abs || item.profit || 0,
        count: item.count
      }));
  };

  // 准备盈利分布饼图数据
  const prepareProfitDistributionData = () => {
    if (!data?.performance_by_pair) return [];
    
    const profitable = data.performance_by_pair.filter(p => (p.profit_abs || p.profit || 0) > 0);
    const losing = data.performance_by_pair.filter(p => (p.profit_abs || p.profit || 0) < 0);
    
    return [
      {
        type: '盈利交易对',
        value: profitable.length,
        profit: profitable.reduce((sum, p) => sum + (p.profit_abs || p.profit || 0), 0)
      },
      {
        type: '亏损交易对',
        value: losing.length,
        profit: Math.abs(losing.reduce((sum, p) => sum + (p.profit_abs || p.profit || 0), 0))
      }
    ];
  };

  // 交易对性能表格列
  const pairColumns: ColumnsType<PairPerformance> = [
    {
      title: '交易对',
      dataIndex: 'pair',
      key: 'pair',
      render: (pair: string) => <strong>{pair}</strong>,
    },
    {
      title: '交易次数',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count,
    },
    {
      title: '总收益',
      dataIndex: 'profit_abs',
      key: 'profit_abs',
      render: (profit: number) => (
        <span style={{ color: profit >= 0 ? '#3f8600' : '#cf1322' }}>
          {profit >= 0 ? <RiseOutlined /> : <FallOutlined />}
          ${Math.abs(profit).toFixed(4)}
        </span>
      ),
      sorter: (a, b) => (a.profit_abs || 0) - (b.profit_abs || 0),
    },
    {
      title: '收益率',
      dataIndex: 'profit',
      key: 'profit',
      render: (profit: number) => (
        <span style={{ color: profit >= 0 ? '#3f8600' : '#cf1322' }}>
          {profit >= 0 ? '+' : ''}{(profit * 100).toFixed(2)}%
        </span>
      ),
      sorter: (a, b) => a.profit - b.profit,
    },
  ];

  if (loading && !data) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="加载性能数据..." />
      </div>
    );
  }

  if (error && !data) {
    return (
      <Alert
        message="数据获取失败"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  const dailyProfitData = prepareDailyProfitData();
  const pairPerformanceData = preparePairPerformanceData();
  const profitDistributionData = prepareProfitDistributionData();

  return (
    <div>
      {/* 控制面板 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <span>时间范围：</span>
            <Select value={timeRange} onChange={setTimeRange} style={{ width: 120 }}>
              <Option value="7">最近7天</Option>
              <Option value="30">最近30天</Option>
              <Option value="90">最近90天</Option>
            </Select>
          </Col>
          <Col>
            <span>图表类型：</span>
            <Select value={chartType} onChange={setChartType} style={{ width: 120 }}>
              <Option value="daily">每日收益</Option>
              <Option value="cumulative">累计收益</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 关键性能指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总交易数"
              value={data?.monthly_stats.total_trades || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="盈利交易"
              value={data?.monthly_stats.profitable_trades || 0}
              valueStyle={{ color: '#3f8600' }}
              prefix={<RiseOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="胜率"
              value={data?.monthly_stats.win_rate || 0}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="盈利因子"
              value={data?.monthly_stats.profit_factor || 0}
              precision={2}
              valueStyle={{ 
                color: (data?.monthly_stats.profit_factor || 0) > 1 ? '#3f8600' : '#cf1322' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        {/* 每日收益图表 */}
        <Col xs={24} lg={16}>
          <Card title="每日收益趋势" loading={loading}>
            {dailyProfitData.length > 0 ? (
              <Line
                data={dailyProfitData}
                xField="date"
                yField="profit"
                seriesField="type"
                color={['#3f8600', '#cf1322']}
                point={{
                  size: 3,
                  shape: 'circle',
                }}
                tooltip={{
                  formatter: (datum: any) => ({
                    name: datum.type,
                    value: `$${datum.profit.toFixed(4)}`,
                  }),
                }}
                yAxis={{
                  label: {
                    formatter: (value: any) => `$${value}`,
                  },
                }}
                height={300}
                smooth
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                暂无收益数据
              </div>
            )}
          </Card>
        </Col>

        {/* 盈利分布饼图 */}
        <Col xs={24} lg={8}>
          <Card title="盈利分布" loading={loading}>
            {profitDistributionData.length > 0 ? (
              <Pie
                data={profitDistributionData}
                angleField="value"
                colorField="type"
                radius={0.8}
                label={{
                  type: 'outer',
                  content: '{name}\n{percentage}',
                }}
                interactions={[{ type: 'element-active' }]}
                color={['#3f8600', '#cf1322']}
                height={300}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                暂无分布数据
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 交易对性能柱状图 */}
      <Card title="交易对收益排行" style={{ marginTop: 16 }} loading={loading}>
        {pairPerformanceData.length > 0 ? (
          <Column
            data={pairPerformanceData}
            xField="pair"
            yField="profit"
            color={(datum: any) => datum.profit >= 0 ? '#3f8600' : '#cf1322'}
            tooltip={{
              formatter: (datum: any) => ({
                name: '收益',
                value: `$${datum.profit.toFixed(4)} (${datum.count}次交易)`,
              }),
            }}
            yAxis={{
              label: {
                formatter: (value: any) => `$${value}`,
              },
            }}
            height={300}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
            暂无交易对数据
          </div>
        )}
      </Card>

      {/* 详细性能表格 */}
      <Card title="交易对详细性能" style={{ marginTop: 16 }}>
        <Table
          columns={pairColumns}
          dataSource={data?.performance_by_pair || []}
          rowKey="pair"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个交易对`,
          }}
          loading={loading}
          scroll={{ x: 500 }}
        />
      </Card>
    </div>
  );
};

export default Performance; 