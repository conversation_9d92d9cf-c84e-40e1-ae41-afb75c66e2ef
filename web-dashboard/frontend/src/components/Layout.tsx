import React, { useState } from 'react';
import { Layout as AntLayout, Menu, theme, Typography, Breadcrumb } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  DashboardOutlined,
  StockOutlined,
  BarChartOutlined,
  HistoryOutlined,
  MonitorOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/trading',
      icon: <StockOutlined />,
      label: '交易管理',
    },
    {
      key: '/strategy',
      icon: <BarChartOutlined />,
      label: '策略管理',
    },
    {
      key: '/backtest',
      icon: <HistoryOutlined />,
      label: '回测分析',
    },
    {
      key: '/monitoring',
      icon: <MonitorOutlined />,
      label: '系统监控',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const getCurrentPageName = () => {
    const currentItem = menuItems.find(item => item.key === location.pathname);
    return currentItem?.label || '仪表板';
  };

  return (
    <AntLayout className="dashboard-layout">
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="sidebar"
      >
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'FT' : 'Freqtrade'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      
      <AntLayout>
        <Header style={{ 
          padding: 0, 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <div
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
              }}
              onClick={() => setCollapsed(!collapsed)}
            >
              {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </div>
            <Title level={4} style={{ margin: 0, flex: 1 }}>
              {getCurrentPageName()}
            </Title>
          </div>
        </Header>
        
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: 6,
          }}
        >
          <Breadcrumb style={{ margin: '0 0 16px 0' }}>
            <Breadcrumb.Item>量化交易系统</Breadcrumb.Item>
            <Breadcrumb.Item>{getCurrentPageName()}</Breadcrumb.Item>
          </Breadcrumb>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout; 