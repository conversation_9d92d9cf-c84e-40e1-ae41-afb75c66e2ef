import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Alert, Spin, Tag, Progress } from 'antd';
import { 
  DollarOutlined, 
  TrophyOutlined, 
  SwapOutlined, 
  RiseOutlined,
  FallOutlined,
  ReloadOutlined,
  ApiOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface DashboardData {
  status: string;
  total_profit: number;
  total_profit_percentage: number;
  open_trades: number;
  closed_trades: number;
  win_rate: number;
  balance: any;
  last_update: string;
}

interface Trade {
  id: number;
  pair: string;
  side: string;
  amount: number;
  price: number;
  profit: number;
  profit_percentage: number;
  status: string;
  timestamp: string;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 并行获取数据
      const [overviewResponse, tradesResponse] = await Promise.all([
        fetch('/api/v1/dashboard/overview'),
        fetch('/api/v1/dashboard/trades/recent?limit=10')
      ]);

      if (!overviewResponse.ok || !tradesResponse.ok) {
        throw new Error('获取数据失败');
      }

      const overviewData = await overviewResponse.json();
      const tradesData = await tradesResponse.json();

      setData(overviewData);
      setTrades(tradesData.trades || []);

    } catch (err) {
      console.error('获取仪表板数据失败:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchDashboardData();
    
    // 设置定时刷新
    const interval = setInterval(fetchDashboardData, 10000); // 每10秒刷新
    
    return () => clearInterval(interval);
  }, []);

  // 交易表格列定义
  const tradeColumns: ColumnsType<Trade> = [
    {
      title: '交易对',
      dataIndex: 'pair',
      key: 'pair',
      render: (pair: string) => (
        <Tag color="blue">{pair}</Tag>
      ),
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      render: (side: string) => (
        <Tag color={side === 'buy' ? 'green' : 'red'}>
          {side === 'buy' ? '买入' : '卖出'}
        </Tag>
      ),
    },
    {
      title: '数量',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => amount.toFixed(6),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `$${price.toFixed(2)}`,
    },
    {
      title: '盈亏',
      dataIndex: 'profit',
      key: 'profit',
      render: (profit: number, record: Trade) => (
        <span style={{ color: profit >= 0 ? '#3f8600' : '#cf1322' }}>
          {profit >= 0 ? <RiseOutlined /> : <FallOutlined />}
          ${Math.abs(profit).toFixed(4)} ({record.profit_percentage.toFixed(2)}%)
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'open' ? 'processing' : 'success'}>
          {status === 'open' ? '开仓' : '已平仓'}
        </Tag>
      ),
    },
  ];

  // 获取状态显示配置
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'running':
        return { color: '#52c41a', text: '运行中', icon: <ApiOutlined /> };
      case 'offline':
        return { color: '#ff4d4f', text: '离线', icon: <ApiOutlined /> };
      default:
        return { color: '#faad14', text: '未知', icon: <ApiOutlined /> };
    }
  };

  if (loading && !data) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (error && !data) {
    return (
      <Alert
        message="数据获取失败"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={fetchDashboardData} style={{ border: 'none', background: 'none', cursor: 'pointer' }}>
            <ReloadOutlined /> 重试
          </button>
        }
      />
    );
  }

  const statusConfig = getStatusConfig(data?.status || 'unknown');

  return (
    <div>
      {/* 系统状态警告 */}
      {data?.status === 'offline' && (
        <Alert
          message="系统离线"
          description="无法连接到Freqtrade API，请检查服务状态"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 关键指标卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={statusConfig.text}
              prefix={statusConfig.icon}
              valueStyle={{ color: statusConfig.color }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总收益"
              value={data?.total_profit || 0}
              precision={4}
              prefix={<DollarOutlined />}
              valueStyle={{ color: (data?.total_profit || 0) >= 0 ? '#3f8600' : '#cf1322' }}
              suffix={`(${(data?.total_profit_percentage || 0).toFixed(2)}%)`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="开仓交易"
              value={data?.open_trades || 0}
              prefix={<SwapOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="胜率"
              value={data?.win_rate || 0}
              precision={1}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress
              percent={data?.win_rate || 0}
              size="small"
              strokeColor={{
                '0%': '#ff4d4f',
                '50%': '#faad14',
                '100%': '#52c41a',
              }}
              showInfo={false}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 交易统计 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} md={12}>
          <Card title="交易统计" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="总交易数"
                  value={(data?.open_trades || 0) + (data?.closed_trades || 0)}
                  valueStyle={{ fontSize: '18px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="已平仓"
                  value={data?.closed_trades || 0}
                  valueStyle={{ fontSize: '18px' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="账户余额" size="small" loading={!data?.balance}>
            {data?.balance && Object.keys(data.balance).length > 0 ? (
              <div>
                {Object.entries(data.balance).map(([currency, amount]: [string, any]) => (
                  <div key={currency} style={{ marginBottom: 8 }}>
                    <Tag color="blue">{currency}</Tag>
                    <span style={{ fontWeight: 'bold' }}>
                      {typeof amount === 'object' ? amount.free || 0 : amount}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ color: '#999' }}>暂无余额数据</div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 最近交易 */}
      <Card 
        title="最近交易" 
        style={{ marginTop: 16 }}
        extra={
          <button 
            onClick={fetchDashboardData} 
            style={{ border: 'none', background: 'none', cursor: 'pointer' }}
            disabled={loading}
          >
            <ReloadOutlined spin={loading} /> 刷新
          </button>
        }
      >
        <Table
          columns={tradeColumns}
          dataSource={trades}
          rowKey="id"
          pagination={false}
          size="small"
          scroll={{ x: 600 }}
          locale={{
            emptyText: '暂无交易记录'
          }}
        />
      </Card>

      {/* 最后更新时间 */}
      {data?.last_update && (
        <div style={{ textAlign: 'center', marginTop: 16, color: '#999', fontSize: '12px' }}>
          最后更新: {new Date(data.last_update).toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default Dashboard; 