import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Form,
  Select,
  DatePicker,
  InputNumber,
  Row,
  Col,
  Divider,
  Table,
  Typography,
  Space,
  Spin,
  message,
  Progress,
  Statistic,
  Tag,
  Alert,
  Tabs,
  Modal
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  DownloadOutlined,
  BarChartOutlined,
  FileTextOutlined,
  RiseOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';
import axios from 'axios';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

interface BacktestConfig {
  strategy: string;
  timeframe: string;
  startDate: string;
  endDate: string;
  initialCash: number;
  commission: number;
  slippage: number;
}

interface BacktestResult {
  id: string;
  strategyName: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  config: BacktestConfig;
  results?: {
    totalReturn: number;
    annualReturn: number;
    maxDrawdown: number;
    sharpeRatio: number;
    sortinoRatio: number;
    winRate: number;
    totalTrades: number;
    profitFactor: number;
    volatility: number;
    monthly_returns?: Array<{ month: string; return: number }>; // Added for new charts
    equity_curve?: Array<{ date: string; equity: number }>; // Added for cumulative returns chart
  };
  equity: Array<{ date: string; value: number }>;
  trades: Array<{
    id: string;
    date: string;
    side: 'buy' | 'sell';
    pair: string;
    price: number;
    exitPrice?: number;
    quantity: number;
    profit: number;
    profitPct?: number;
    duration?: string;
    fees?: number;
    status?: string;
  }>;
  createdAt: string;
  completedAt?: string;
}

const Backtest: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [runningBacktest, setRunningBacktest] = useState<string | null>(null);
  const [backtestResults, setBacktestResults] = useState<BacktestResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<BacktestResult | null>(null);
  const [strategies, setStrategies] = useState<string[]>([]);
  const [availablePairs, setAvailablePairs] = useState<string[]>(['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT', 'SOL/USDT', 'DOT/USDT', 'DOGE/USDT']);
  
  // 日志相关状态
  const [showLogs, setShowLogs] = useState(false);
  const [currentLogsId, setCurrentLogsId] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [logsLoading, setLogsLoading] = useState(false);

  useEffect(() => {
    fetchStrategies();
    fetchBacktestResults();
  }, []);

  const fetchStrategies = async () => {
    try {
      const response = await axios.get('/api/v1/strategies/available/');
      setStrategies(response.data.map((s: any) => s.name));
    } catch (error) {
      // 模拟数据
      setStrategies(['SimpleModel3Strategy', 'EMACrossStrategy', 'RSIStrategy']);
    }
  };

  const fetchBacktestResults = async () => {
    try {
      console.log('📊 获取回测结果...');
      const response = await axios.get('/api/v1/backtests/', {
        timeout: 15000, // 15秒超时
        headers: {
          'Accept': 'application/json'
        },
        validateStatus: function (status) {
          return status >= 200 && status < 500; // 只有状态码大于等于500时才视为错误
        }
      });
      console.log('✅ 回测结果响应:', response.data);
      
      // 检查响应状态
      if (response.status >= 400) {
        throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
      }
      
      // 转换后端数据格式为前端期望的格式
      const transformedResults: BacktestResult[] = await Promise.all(
        response.data.map(async (item: any) => {
          let detailedResults = null;
          let equityCurve: Array<{ date: string; value: number }> = [];
          let trades: Array<{
            id: string;
            date: string;
            side: 'buy' | 'sell';
            pair: string;
            price: number;
            exitPrice?: number;
            quantity: number;
            profit: number;
            profitPct?: number;
            duration?: string;
            fees?: number;
            status?: string;
          }> = [];
          
          // 如果回测已完成，获取详细结果
          if (item.status === 'completed') {
            try {
              const resultsResponse = await axios.get(`/api/v1/backtests/${item.id}/results`, {
                timeout: 10000, // 10秒超时
                headers: {
                  'Accept': 'application/json'
                }
              });
              const tradesResponse = await axios.get(`/api/v1/backtests/${item.id}/trades`, {
                timeout: 10000, // 10秒超时
                headers: {
                  'Accept': 'application/json'
                }
              });
              
              detailedResults = resultsResponse.data;
              
              // 转换资金曲线数据格式
              if (detailedResults.equity_curve && Array.isArray(detailedResults.equity_curve)) {
                equityCurve = detailedResults.equity_curve.map((point: any) => ({
                  date: point.date || '',
                  value: point.equity || 0
                }));
              }
              
              // 转换交易记录数据格式
              if (tradesResponse.data && Array.isArray(tradesResponse.data)) {
                trades = tradesResponse.data.map((trade: any) => ({
                id: trade.id,
                date: trade.entry_time,
                side: trade.side,
                pair: trade.pair,
                price: trade.entry_price,
                exitPrice: trade.exit_price,
                quantity: trade.amount,
                profit: trade.profit,
                profitPct: trade.profit_pct,
                duration: trade.duration,
                fees: trade.fees,
                status: trade.status
                }));
              }
              
            } catch (detailError) {
              console.warn('获取详细结果失败:', detailError);
            }
          }
          
          return {
            id: item.id,
            strategyName: item.strategy_name || item.name,
            status: item.status,
            progress: item.status === 'completed' ? 100 : item.status === 'running' ? 50 : 0,
            config: {
              strategy: item.strategy_name || item.strategy_id,
              timeframe: item.config?.timeframe || '1h',
              startDate: item.start_date,
              endDate: item.end_date,
              initialCash: item.initial_balance,
              commission: item.config?.commission || 0.1,
              slippage: item.config?.slippage || 0.05
            },
            results: item.status === 'completed' && detailedResults ? {
              totalReturn: item.total_return || 0,
              annualReturn: detailedResults.risk_metrics?.annual_return || (item.total_return || 0),
              maxDrawdown: Math.abs(item.max_drawdown || 0),
              sharpeRatio: item.sharpe_ratio || 0,
              sortinoRatio: detailedResults.risk_metrics?.sortino_ratio || item.sharpe_ratio * 1.2 || 0,
              winRate: item.win_rate || 0,
              totalTrades: item.total_trades || 0,
              profitFactor: 1.2, // 默认值
              volatility: detailedResults.risk_metrics?.volatility || 0.15,
              monthly_returns: detailedResults.monthly_returns, // Added for new charts
              equity_curve: detailedResults.equity_curve || [] // Added for cumulative returns chart
            } : item.status === 'completed' ? {
              totalReturn: item.total_return || 0,
              annualReturn: (item.total_return || 0),
              maxDrawdown: Math.abs(item.max_drawdown || 0),
              sharpeRatio: item.sharpe_ratio || 0,
              sortinoRatio: item.sharpe_ratio * 1.2 || 0,
              winRate: item.win_rate || 0,
              totalTrades: item.total_trades || 0,
              profitFactor: 1.2,
              volatility: 0.15,
              monthly_returns: [], // Added for new charts
              equity_curve: [] // Added for cumulative returns chart
            } : undefined,
            equity: equityCurve.length > 0 ? equityCurve : [],
            trades: trades.length > 0 ? trades : [],
            createdAt: item.created_at,
            completedAt: item.updated_at
          };
        })
      );
      
      setBacktestResults(transformedResults);
      if (transformedResults.length > 0) {
        setSelectedResult(transformedResults[0]);
      }
    } catch (error: any) {
      console.error('获取回测结果失败:', error);
      
      // 如果API失败，显示模拟数据作为示例
      const mockResults: BacktestResult[] = [
        {
          id: 'demo-1',
          strategyName: 'EMACrossStrategy',
          status: 'completed',
          progress: 100,
          config: {
            strategy: 'EMACrossStrategy',
            timeframe: '1h',
            startDate: '2024-01-01',
            endDate: '2024-12-01',
            initialCash: 10000,
            commission: 0.1,
            slippage: 0.05
          },
          results: {
            totalReturn: 12.35,
            annualReturn: 12.35,
            maxDrawdown: 8.42,
            sharpeRatio: 1.85,
            sortinoRatio: 2.1,
            winRate: 68.5,
            totalTrades: 147,
            profitFactor: 1.45,
            volatility: 0.18,
            monthly_returns: [], // Added for new charts
            equity_curve: Array.from({ length: 31 }, (_, i) => ({
              date: dayjs('2024-01-01').add(i, 'day').format('YYYY-MM-DD'),
              equity: 10000 + Math.random() * 1250 + i * 40
            })) // Added for cumulative returns chart
          },
          equity: Array.from({ length: 31 }, (_, i) => ({
            date: dayjs('2024-01-01').add(i, 'day').format('YYYY-MM-DD'),
            value: 10000 + Math.random() * 1250 + i * 40
          })),
          trades: Array.from({ length: 20 }, (_, i) => ({
            id: `mock-${i}`,
            date: dayjs('2024-01-01').add(i, 'day').format('YYYY-MM-DD HH:mm:ss'),
            side: Math.random() > 0.5 ? 'buy' : 'sell' as 'buy' | 'sell',
            pair: 'BTC/USDT',
            price: 50000 + Math.random() * 10000,
            exitPrice: 50000 + Math.random() * 10000,
            quantity: Math.random() * 0.1,
            profit: (Math.random() - 0.5) * 500,
            profitPct: (Math.random() - 0.5) * 10,
            duration: `${Math.floor(Math.random() * 1000)} minutes`,
            fees: Math.random() * 10,
            status: 'closed'
          })),
          createdAt: '2024-01-01T10:00:00Z',
          completedAt: '2024-01-01T15:30:00Z'
        }
      ];
      setBacktestResults(mockResults);
      setSelectedResult(mockResults[0]);
      
      message.warning('无法连接到后端服务，显示示例数据');
    }
  };

  const handleStartBacktest = async (values: any) => {
    setLoading(true);
    try {
      // 构造符合后端 BacktestCreate 结构的数据
      const config = {
        name: `${values.strategy} 回测 - ${dayjs().format('YYYY-MM-DD HH:mm')}`,
        strategy_id: values.strategy,
        strategy_name: values.strategy,
        start_date: values.dateRange[0].format('YYYY-MM-DD HH:mm:ss'),
        end_date: values.dateRange[1].format('YYYY-MM-DD HH:mm:ss'),
        initial_balance: values.initialCash || 10000,
        timeframe: values.timeframe || '1h',
        commission: values.commission || 0.1,
        slippage: values.slippage || 0.05,
        max_open_trades: values.maxOpenTrades || 3,
        stake_amount: values.stakeAmount || 100,
        pairs: values.pairs || ['BTC/USDT']
      };

      console.log('🚀 发送回测请求:', config);
      
      // 更新请求配置，添加超时和错误处理
      const response = await axios.post('/api/v1/backtests/', config, {
        timeout: 30000, // 30秒超时
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        validateStatus: function (status) {
          return status >= 200 && status < 500; // 只有状态码大于等于500时才视为错误
        }
      });
      
      console.log('✅ 回测响应:', response.data);
      
      // 检查响应状态
      if (response.status >= 400) {
        throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
      }
      
      const backtestId = response.data.backtest_id || response.data.id;
      setRunningBacktest(backtestId);
      message.success('回测已开始！');
      
      // 立即刷新回测列表以显示新创建的回测记录
      await fetchBacktestResults();
      
      // 设置轮询检查回测状态 - 只对运行中的回测进行轮询
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await axios.get(`/api/v1/backtests/${backtestId}`, {
            timeout: 10000, // 10秒超时
            headers: {
              'Accept': 'application/json'
            }
          });
          const status = statusResponse.data.status;
          
          if (status === 'completed') {
            clearInterval(pollInterval);
            setRunningBacktest(null);
            await fetchBacktestResults();
            message.success('回测完成！');
          } else if (status === 'failed') {
            clearInterval(pollInterval);
            setRunningBacktest(null);
            await fetchBacktestResults();
            message.error('回测失败');
          } else if (status !== 'running') {
            // 如果状态不是running，也停止轮询
            clearInterval(pollInterval);
            setRunningBacktest(null);
            await fetchBacktestResults();
          } else {
            // 如果仍在运行，定期刷新列表以保持同步
            await fetchBacktestResults();
          }
        } catch (error) {
          console.error('轮询回测状态失败:', error);
          // 轮询失败时也停止轮询，避免无限错误
          clearInterval(pollInterval);
          setRunningBacktest(null);
        }
      }, 3000); // 每3秒检查一次状态，避免过于频繁
      
      // 10分钟后停止轮询
      setTimeout(() => {
        clearInterval(pollInterval);
        if (runningBacktest) {
          setRunningBacktest(null);
          message.warning('回测超时，请手动刷新查看结果');
        }
      }, 600000);
      
    } catch (error: any) {
      console.error('回测启动失败:', error);
      const errorMsg = error.response?.data?.detail || error.message || '启动回测失败';
      message.error(`启动回测失败: ${errorMsg}`);
    }
    setLoading(false);
  };

  const handleStopBacktest = async () => {
    if (runningBacktest) {
      try {
        await axios.post(`/api/v1/backtests/${runningBacktest}/stop`);
        setRunningBacktest(null);
        message.success('回测已停止');
      } catch (error) {
        message.error('停止回测失败');
      }
    }
  };

  const handleDownloadReport = async (backtestId: string, format: string = 'json') => {
    try {
      const response = await axios.get(`/api/v1/backtests/${backtestId}/download?format=${format}`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `backtest_report_${backtestId}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      message.success('报告下载成功');
    } catch (error: any) {
      console.error('下载报告失败:', error);
      message.error('下载报告失败');
    }
  };

  const handleShowLogs = async (backtestId: string) => {
    setCurrentLogsId(backtestId);
    setShowLogs(true);
    await fetchLogs(backtestId);
  };

  const fetchLogs = async (backtestId: string) => {
    setLogsLoading(true);
    try {
      const response = await axios.get(`/api/v1/backtests/${backtestId}/logs?lines=200`, {
        timeout: 10000,
        headers: {
          'Accept': 'application/json'
        }
      });
      
      setLogs(response.data.logs || []);
    } catch (error: any) {
      console.error('获取日志失败:', error);
      message.error('获取日志失败');
      setLogs(['获取日志失败: ' + (error.response?.data?.detail || error.message)]);
    }
    setLogsLoading(false);
  };

  const handleRefreshLogs = () => {
    if (currentLogsId) {
      fetchLogs(currentLogsId);
    }
  };

  const getEquityChartOption = () => {
    if (!selectedResult?.equity || selectedResult.equity.length === 0) return {};
    
    return {
      title: {
        text: '资金曲线',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          return `日期: ${data.axisValue}<br/>资金: $${data.value.toLocaleString()}`;
        }
      },
      xAxis: {
        type: 'category',
        data: selectedResult.equity.map(e => e?.date || '')
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '${value}'
        }
      },
      series: [{
        data: selectedResult.equity.map(e => e?.value || 0),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: 'rgba(24, 144, 255, 0.1)'
        }
      }],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };
  };

  const getDrawdownChartOption = () => {
    if (!selectedResult?.equity || selectedResult.equity.length === 0) return {};
    
    const firstPoint = selectedResult.equity[0];
    if (!firstPoint || typeof firstPoint.value !== 'number') return {};
    
    const peak = firstPoint.value;
    const drawdowns = selectedResult.equity.map(e => {
      if (!e || typeof e.value !== 'number') return 0;
      return ((e.value - peak) / peak) * 100;
    });

    return {
      title: {
        text: '回撤曲线',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          return `日期: ${data.axisValue}<br/>回撤: ${data.value.toFixed(2)}%`;
        }
      },
      xAxis: {
        type: 'category',
        data: selectedResult.equity.map(e => e?.date || '')
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [{
        data: drawdowns,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#ff4d4f'
        },
        areaStyle: {
          color: 'rgba(255, 77, 79, 0.1)'
        }
      }],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };
  };

  // 获取月度收益图表配置
  const getMonthlyReturnsChartOption = () => {
    if (!selectedResult?.results?.monthly_returns) return {};
    
    const monthlyReturns = selectedResult.results.monthly_returns;
    const months = monthlyReturns.map((item: any) => item.month);
    const returns = monthlyReturns.map((item: any) => item.return);
    
    return {
      title: {
        text: '月度收益率',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLabel: { rotate: 45 }
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: '{value}%' }
      },
      series: [{
        data: returns.map((value: number) => ({
          value,
          itemStyle: { color: value >= 0 ? '#52c41a' : '#ff4d4f' }
        })),
        type: 'bar',
        itemStyle: { borderRadius: [4, 4, 0, 0] }
      }],
      grid: { top: '15%', bottom: '15%', left: '10%', right: '10%' }
    };
  };

  // 获取收益分布图表配置
  const getReturnsDistributionChartOption = () => {
    if (!selectedResult?.trades || selectedResult.trades.length === 0) return {};
    
    const trades = selectedResult.trades;
    const returns = trades.map((trade: any) => trade.profitPct || 0);
    
    // 计算收益分布区间
    const bins = [-10, -5, -2, -1, 0, 1, 2, 5, 10];
    const distribution = bins.map((bin, index) => {
      if (index === 0) return returns.filter(r => r < bin).length;
      if (index === bins.length - 1) return returns.filter(r => r >= bins[index - 1]).length;
      return returns.filter(r => r >= bins[index - 1] && r < bin).length;
    });
    
    const labels = [
      '< -10%', '-10% ~ -5%', '-5% ~ -2%', '-2% ~ -1%', 
      '-1% ~ 0%', '0% ~ 1%', '1% ~ 2%', '2% ~ 5%', '≥ 5%'
    ];
    
    return {
      title: {
        text: '收益分布',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${data.value}笔交易`;
        }
      },
      xAxis: {
        type: 'category',
        data: labels,
        axisLabel: { rotate: 45, fontSize: 10 }
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: '{value}笔' }
      },
      series: [{
        data: distribution.map((value, index) => ({
          value,
          itemStyle: { 
            color: index < 4 ? '#ff4d4f' : index === 4 ? '#faad14' : '#52c41a'
          }
        })),
        type: 'bar',
        itemStyle: { borderRadius: [4, 4, 0, 0] }
      }],
      grid: { top: '15%', bottom: '25%', left: '10%', right: '10%' }
    };
  };

  // 获取风险收益散点图配置
  const getRiskReturnScatterOption = () => {
    if (!selectedResult?.trades || selectedResult.trades.length === 0) return {};
    
    const trades = selectedResult.trades;
    const monthlyData: any = {};
    
    // 按月分组计算收益和波动率
    trades.forEach((trade: any) => {
      const month = trade.date.substring(0, 7);
      if (!monthlyData[month]) {
        monthlyData[month] = [];
      }
      monthlyData[month].push(trade.profitPct || 0);
    });
    
    const scatterData = Object.entries(monthlyData).map(([month, returns]: [string, any]) => {
      const avgReturn = returns.reduce((sum: number, r: number) => sum + r, 0) / returns.length;
      const variance = returns.reduce((sum: number, r: number) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
      const volatility = Math.sqrt(variance);
      
      return [volatility, avgReturn, month];
    });
    
    return {
      title: {
        text: '风险收益散点图',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const [volatility, avgReturn, month] = params.data;
          return `${month}<br/>收益率: ${avgReturn.toFixed(2)}%<br/>波动率: ${volatility.toFixed(2)}%`;
        }
      },
      xAxis: {
        type: 'value',
        name: '波动率 (%)',
        nameLocation: 'middle',
        nameGap: 25,
        axisLabel: { formatter: '{value}%' }
      },
      yAxis: {
        type: 'value',
        name: '平均收益率 (%)',
        nameLocation: 'middle',
        nameGap: 35,
        axisLabel: { formatter: '{value}%' }
      },
      series: [{
        data: scatterData,
        type: 'scatter',
        symbolSize: 8,
        itemStyle: { color: '#1890ff' }
      }],
      grid: { top: '15%', bottom: '15%', left: '15%', right: '10%' }
    };
  };

  // 获取交易时长分布图配置
  const getTradeDurationDistributionOption = () => {
    if (!selectedResult?.trades || selectedResult.trades.length === 0) return {};
    
    const trades = selectedResult.trades;
    const durations = trades.map((trade: any) => {
      const duration = trade.duration || '0 minutes';
      const minutes = parseInt(duration.split(' ')[0]) || 0;
      
      if (minutes < 60) return '< 1小时';
      if (minutes < 240) return '1-4小时';
      if (minutes < 1440) return '4-24小时';
      if (minutes < 10080) return '1-7天';
      return '> 7天';
    });
    
    const categories = ['< 1小时', '1-4小时', '4-24小时', '1-7天', '> 7天'];
    const distribution = categories.map(cat => durations.filter(d => d === cat).length);
    
    return {
      title: {
        text: '交易时长分布',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}笔 ({d}%)'
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: categories.map((name, index) => ({
          name,
          value: distribution[index]
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };
  };

  // 获取累计收益vs基准对比图配置
  const getCumulativeReturnsVsBenchmarkOption = () => {
    if (!selectedResult?.results?.equity_curve || selectedResult.results.equity_curve.length === 0) return {};
    
    const equityCurve = selectedResult.results.equity_curve;
    const initialBalance = selectedResult.config.initialCash || 10000;
    
    // 计算累计收益率
    const dates = equityCurve.map((item: any) => item?.date || '');
    const portfolioReturns = equityCurve.map((item: any) => 
      ((item?.equity || 0) - initialBalance) / initialBalance * 100
    );
    
    // 模拟基准收益（比如市场平均收益）
    const benchmarkReturns = dates.map((_: any, index: number) => {
      const days = index;
      const annualReturn = 8; // 假设年化8%基准收益
      return (days / 365) * annualReturn;
    });
    
    return {
      title: {
        text: '累计收益 vs 基准对比',
        left: 'center',
        textStyle: { fontSize: 14, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const date = params[0].axisValue;
          let result = `${date}<br/>`;
          params.forEach((param: any) => {
            result += `${param.seriesName}: ${param.value.toFixed(2)}%<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['策略收益', '基准收益'],
        top: '8%'
      },
      xAxis: {
        type: 'category',
        data: dates.map((date: string) => dayjs(date).format('MM-DD'))
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: '{value}%' }
      },
      series: [
        {
          name: '策略收益',
          data: portfolioReturns,
          type: 'line',
          smooth: true,
          lineStyle: { color: '#1890ff', width: 2 },
          areaStyle: { color: 'rgba(24, 144, 255, 0.1)' }
        },
        {
          name: '基准收益',
          data: benchmarkReturns,
          type: 'line',
          smooth: true,
          lineStyle: { color: '#52c41a', width: 2, type: 'dashed' }
        }
      ],
      grid: { top: '20%', bottom: '10%', left: '10%', right: '10%' }
    };
  };

  const resultsColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <Text copyable={{ text: id }} style={{ fontSize: '12px', fontFamily: 'monospace' }}>
          {id.substring(0, 8)}...
        </Text>
      ),
    },
    {
      title: '策略名称',
      dataIndex: 'strategyName',
      key: 'strategyName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: BacktestResult) => {
        const statusMap = {
          pending: { color: 'default', label: '等待中' },
          running: { color: 'processing', label: '运行中' },
          completed: { color: 'success', label: '已完成' },
          failed: { color: 'error', label: '失败' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', label: status || '未知' };
        return (
          <Space>
            <Tag color={statusInfo.color}>{statusInfo.label}</Tag>
            {status === 'running' && <Progress percent={record.progress} size="small" />}
          </Space>
        );
      },
    },
    {
      title: '总收益率',
      dataIndex: ['results', 'totalReturn'],
      key: 'totalReturn',
      render: (value?: number) => value !== undefined ? (
        <Text type={value >= 0 ? 'success' : 'danger'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      ) : '-',
    },
    {
      title: '最大回撤',
      dataIndex: ['results', 'maxDrawdown'],
      key: 'maxDrawdown',
      render: (value?: number) => value !== undefined ? (
        <Text type="danger">{value.toFixed(2)}%</Text>
      ) : '-',
    },
    {
      title: '夏普比率',
      dataIndex: ['results', 'sharpeRatio'],
      key: 'sharpeRatio',
      render: (value?: number) => value !== undefined ? value.toFixed(2) : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (value: string) => dayjs(value).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: BacktestResult) => (
        <Space>
          <Button
            size="small"
            type="primary"
            icon={<BarChartOutlined />}
            onClick={() => setSelectedResult(record)}
            disabled={record.status !== 'completed'}
          >
            查看详情
          </Button>
          <Button
            size="small"
            icon={<DownloadOutlined />}
            disabled={record.status !== 'completed'}
            onClick={() => handleDownloadReport(record.id, 'json')}
          >
            导出报告
          </Button>
          <Button
            size="small"
            icon={<FileTextOutlined />}
            onClick={() => handleShowLogs(record.id)}
          >
            查看日志
          </Button>
        </Space>
      ),
    },
  ];

  const tradesColumns = [
    {
      title: '交易对',
      dataIndex: 'pair',
      key: 'pair',
      render: (value: string) => <Tag color="blue">{value}</Tag>,
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      render: (side: string) => (
        <Tag color={side === 'buy' ? 'green' : 'red'}>
          {side === 'buy' ? '买入' : '卖出'}
        </Tag>
      ),
    },
    {
      title: '开仓时间',
      dataIndex: 'date',
      key: 'date',
      render: (value: string) => dayjs(value).format('MM-DD HH:mm'),
    },
    {
      title: '开仓价格',
      dataIndex: 'price',
      key: 'price',
      render: (value: number) => `$${value.toFixed(2)}`,
    },
    {
      title: '平仓价格',
      dataIndex: 'exitPrice',
      key: 'exitPrice',
      render: (value?: number) => value ? `$${value.toFixed(2)}` : '-',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (value: number) => value.toFixed(4),
    },
    {
      title: '持仓时间',
      dataIndex: 'duration',
      key: 'duration',
      render: (value?: string) => value || '-',
    },
    {
      title: '盈亏金额',
      dataIndex: 'profit',
      key: 'profit',
      render: (value: number) => (
        <Text type={value >= 0 ? 'success' : 'danger'}>
          {value >= 0 ? '+' : ''}${value.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '盈亏比例',
      dataIndex: 'profitPct',
      key: 'profitPct',
      render: (value?: number) => value !== undefined ? (
        <Text type={value >= 0 ? 'success' : 'danger'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      ) : '-',
    },
    {
      title: '手续费',
      dataIndex: 'fees',
      key: 'fees',
      render: (value?: number) => value !== undefined ? `$${value.toFixed(2)}` : '-',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>策略回测</Title>

      <Row gutter={24}>
        <Col span={8}>
          <Card title="回测配置" extra={
            runningBacktest ? (
              <Button
                type="primary"
                danger
                icon={<StopOutlined />}
                onClick={handleStopBacktest}
                loading={loading}
              >
                停止回测
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={() => form.submit()}
                loading={loading}
              >
                开始回测
              </Button>
            )
          }>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartBacktest}
              initialValues={{
                timeframe: '5m',
                initialCash: 10000,
                commission: 0.001,
                slippage: 0.0005,
                dateRange: [dayjs().subtract(1, 'month'), dayjs()]
              }}
            >
              <Form.Item
                name="strategy"
                label="选择策略"
                rules={[{ required: true, message: '请选择策略' }]}
              >
                <Select placeholder="请选择策略">
                  {strategies.map(strategy => (
                    <Option key={strategy} value={strategy}>{strategy}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="timeframe"
                label="时间周期"
                rules={[{ required: true, message: '请选择时间周期' }]}
              >
                <Select>
                  <Option value="1m">1分钟</Option>
                  <Option value="5m">5分钟</Option>
                  <Option value="15m">15分钟</Option>
                  <Option value="1h">1小时</Option>
                  <Option value="4h">4小时</Option>
                  <Option value="1d">1天</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="dateRange"
                label="回测时间范围"
                rules={[{ required: true, message: '请选择时间范围' }]}
              >
                <RangePicker 
                  style={{ width: '100%' }} 
                  showTime={{ 
                    format: 'HH:mm:ss',
                    defaultValue: [
                      dayjs('00:00:00', 'HH:mm:ss'),
                      dayjs('23:59:59', 'HH:mm:ss')
                    ]
                  }}
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </Form.Item>

              <Form.Item
                name="initialCash"
                label="初始资金"
                rules={[{ required: true, message: '请输入初始资金' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1000}
                  max={1000000}
                  formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>

              <Form.Item
                name="commission"
                label="手续费率"
                rules={[{ required: true, message: '请输入手续费率' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={0.01}
                  step={0.0001}
                  formatter={value => `${(Number(value) * 100).toFixed(2)}%`}
                />
              </Form.Item>

              <Form.Item
                name="slippage"
                label="滑点"
                rules={[{ required: true, message: '请输入滑点' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={0.01}
                  step={0.0001}
                  formatter={value => `${(Number(value) * 100).toFixed(2)}%`}
                />
              </Form.Item>

              <Form.Item
                name="maxOpenTrades"
                label="最大开仓数"
                initialValue={3}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={10}
                />
              </Form.Item>

              <Form.Item
                name="stakeAmount"
                label="每次交易金额"
                initialValue={100}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={10}
                  max={10000}
                  formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>

              <Form.Item
                name="pairs"
                label="交易对"
                rules={[{ required: true, message: '请选择至少一个交易对' }]}
                initialValue={['BTC/USDT']}
              >
                <Select mode="multiple" placeholder="请选择交易对">
                  {availablePairs.map(pair => (
                    <Option key={pair} value={pair}>{pair}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>

            {runningBacktest && (
              <Alert
                message="回测进行中"
                description="正在处理历史数据，请耐心等待..."
                type="info"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </Card>
        </Col>

        <Col span={16}>
          <Card title="回测结果">
            <Table
              columns={resultsColumns}
              dataSource={backtestResults}
              rowKey="id"
              size="small"
              pagination={{
                pageSize: 5,
                showSizeChanger: false
              }}
            />
          </Card>
        </Col>
      </Row>

      {selectedResult && selectedResult.status === 'completed' && (
        <Card title={`${selectedResult.strategyName} - 详细分析`} style={{ marginTop: '24px' }}>
          <Tabs defaultActiveKey="overview">
            <TabPane tab="概览" key="overview">
              <Row gutter={16} style={{ marginBottom: '24px' }}>
                <Col span={4}>
                  <Statistic
                    title="总收益率"
                    value={selectedResult.results!.totalReturn}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: selectedResult.results!.totalReturn >= 0 ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="年化收益率"
                    value={selectedResult.results!.annualReturn}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: selectedResult.results!.annualReturn >= 0 ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="最大回撤"
                    value={selectedResult.results!.maxDrawdown}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="夏普比率"
                    value={selectedResult.results!.sharpeRatio}
                    precision={2}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="胜率"
                    value={selectedResult.results!.winRate}
                    precision={1}
                    suffix="%"
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="总交易次数"
                    value={selectedResult.results!.totalTrades}
                  />
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <ReactECharts option={getEquityChartOption()} style={{ height: '400px' }} />
                </Col>
                <Col span={12}>
                  <ReactECharts option={getDrawdownChartOption()} style={{ height: '400px' }} />
                </Col>
              </Row>

              <Row gutter={16} style={{ marginTop: '24px' }}>
                <Col span={24}>
                  <ReactECharts option={getMonthlyReturnsChartOption()} style={{ height: '300px' }} />
                </Col>
              </Row>

              <Row gutter={16} style={{ marginTop: '24px' }}>
                <Col span={12}>
                  <ReactECharts option={getReturnsDistributionChartOption()} style={{ height: '350px' }} />
                </Col>
                <Col span={12}>
                  <ReactECharts option={getRiskReturnScatterOption()} style={{ height: '350px' }} />
                </Col>
              </Row>

              <Row gutter={16} style={{ marginTop: '24px' }}>
                <Col span={12}>
                  <ReactECharts option={getTradeDurationDistributionOption()} style={{ height: '350px' }} />
                </Col>
                <Col span={12}>
                  <ReactECharts option={getCumulativeReturnsVsBenchmarkOption()} style={{ height: '350px' }} />
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="交易记录" key="trades">
              <Table
                columns={tradesColumns}
                dataSource={selectedResult.trades}
                rowKey={(record, index) => `${record.date}-${index}`}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条交易记录`
                }}
              />
            </TabPane>

            <TabPane tab="风险指标" key="risk">
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="收益指标">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Statistic title="总收益率" value={selectedResult.results!.totalReturn} suffix="%" />
                      <Statistic title="年化收益率" value={selectedResult.results!.annualReturn} suffix="%" />
                      <Statistic title="盈利因子" value={selectedResult.results!.profitFactor} />
                    </Space>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="风险指标">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Statistic title="最大回撤" value={selectedResult.results!.maxDrawdown} suffix="%" />
                      <Statistic title="波动率" value={selectedResult.results!.volatility * 100} suffix="%" />
                      <Statistic title="索提诺比率" value={selectedResult.results!.sortinoRatio} />
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>
          </Tabs>
        </Card>
      )}

      {/* 日志查看Modal */}
      <Modal
        title={`回测日志 - ${currentLogsId}`}
        open={showLogs}
        onCancel={() => setShowLogs(false)}
        width={800}
        footer={[
          <Button key="refresh" onClick={handleRefreshLogs} loading={logsLoading} icon={<ReloadOutlined />}>
            刷新
          </Button>,
          <Button key="close" onClick={() => setShowLogs(false)}>
            关闭
          </Button>
        ]}
      >
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          {logsLoading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin />
              <div style={{ marginTop: '10px' }}>加载日志中...</div>
            </div>
          ) : (
            <pre style={{ 
              background: '#f6f8fa', 
              padding: '16px', 
              borderRadius: '6px',
              fontSize: '12px',
              lineHeight: '1.5',
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word'
            }}>
              {logs.length > 0 ? logs.join('\n') : '暂无日志'}
            </pre>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default Backtest; 