import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Select,
  message,
  Modal,
  Table,
  Tag,
  Popconfirm
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface Settings {
  trading: {
    maxOpenTrades: number;
    stakeAmount: number;
    stakeCurrency: string;
    dryRun: boolean;
    enableTelegramBot: boolean;
    telegramToken: string;
    telegramChatId: string;
  };
  risk: {
    maxDailyLoss: number;
    maxWeeklyLoss: number;
    stopLossEnabled: boolean;
    trailingStopEnabled: boolean;
    emergencyStopEnabled: boolean;
  };
  api: {
    enableApi: boolean;
    apiUsername: string;
    apiPassword: string;
    corsAllowedOrigins: string[];
    rateLimitEnabled: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
  };
  notifications: {
    emailEnabled: boolean;
    emailSmtpServer: string;
    emailSmtpPort: number;
    emailUsername: string;
    emailPassword: string;
    webhookEnabled: boolean;
    webhookUrl: string;
  };
  performance: {
    enableCaching: boolean;
    cacheTimeout: number;
    databasePoolSize: number;
    logLevel: string;
    enableMetrics: boolean;
  };
}

interface ExchangeAccount {
  id: string;
  name: string;
  exchange: string;
  apiKey: string;
  status: 'active' | 'inactive' | 'error';
  balance: number;
  lastSync: string;
}

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<Settings>({
    trading: {
      maxOpenTrades: 8,
      stakeAmount: 100,
      stakeCurrency: 'USDT',
      dryRun: false,
      enableTelegramBot: true,
      telegramToken: '',
      telegramChatId: ''
    },
    risk: {
      maxDailyLoss: 5,
      maxWeeklyLoss: 15,
      stopLossEnabled: true,
      trailingStopEnabled: true,
      emergencyStopEnabled: true
    },
    api: {
      enableApi: true,
      apiUsername: 'admin',
      apiPassword: '',
      corsAllowedOrigins: ['http://localhost:3000'],
      rateLimitEnabled: true,
      rateLimitRequests: 100,
      rateLimitWindow: 60
    },
    notifications: {
      emailEnabled: false,
      emailSmtpServer: '',
      emailSmtpPort: 587,
      emailUsername: '',
      emailPassword: '',
      webhookEnabled: false,
      webhookUrl: ''
    },
    performance: {
      enableCaching: true,
      cacheTimeout: 300,
      databasePoolSize: 10,
      logLevel: 'INFO',
      enableMetrics: true
    }
  });

  const [exchangeAccounts, setExchangeAccounts] = useState<ExchangeAccount[]>([]);
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<ExchangeAccount | null>(null);
  const [accountForm] = Form.useForm();

  useEffect(() => {
    fetchSettings();
    fetchExchangeAccounts();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await axios.get('/api/v1/settings');
      setSettings(response.data);
      form.setFieldsValue(response.data);
    } catch (error) {
      message.error('获取设置失败');
      form.setFieldsValue(settings);
    }
  };

  const fetchExchangeAccounts = async () => {
    try {
      const response = await axios.get('/api/v1/settings/exchanges');
      setExchangeAccounts(response.data);
    } catch (error) {
      // 模拟数据
      setExchangeAccounts([
        {
          id: '1',
          name: 'Binance主账户',
          exchange: 'binance',
          apiKey: 'xxxxxx...xxxx',
          status: 'active',
          balance: 9996.45,
          lastSync: '2024-01-20 14:30:00'
        }
      ]);
    }
  };

  const handleSaveSettings = async (values: Settings) => {
    setLoading(true);
    try {
      await axios.put('/api/v1/settings', values);
      setSettings(values);
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存设置失败');
    }
    setLoading(false);
  };

  const handleResetSettings = () => {
    Modal.confirm({
      title: '确认重置',
      icon: <ExclamationCircleOutlined />,
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        form.resetFields();
        message.success('设置已重置');
      }
    });
  };

  const handleSaveExchangeAccount = async (values: any) => {
    try {
      if (editingAccount) {
        await axios.put(`/api/v1/settings/exchanges/${editingAccount.id}`, values);
        message.success('交易所账户更新成功');
      } else {
        await axios.post('/api/v1/settings/exchanges', values);
        message.success('交易所账户添加成功');
      }
      setAccountModalVisible(false);
      accountForm.resetFields();
      setEditingAccount(null);
      fetchExchangeAccounts();
    } catch (error) {
      message.error(editingAccount ? '更新账户失败' : '添加账户失败');
    }
  };

  const handleDeleteAccount = async (accountId: string) => {
    try {
      await axios.delete(`/api/v1/settings/exchanges/${accountId}`);
      message.success('删除账户成功');
      fetchExchangeAccounts();
    } catch (error) {
      message.error('删除账户失败');
    }
  };

  const exchangeColumns = [
    {
      title: '账户名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '交易所',
      dataIndex: 'exchange',
      key: 'exchange',
      render: (exchange: string) => exchange.toUpperCase(),
    },
    {
      title: 'API Key',
      dataIndex: 'apiKey',
      key: 'apiKey',
      render: (apiKey: string) => `${apiKey.substring(0, 8)}...${apiKey.slice(-4)}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          active: { color: 'success', label: '活跃' },
          inactive: { color: 'default', label: '不活跃' },
          error: { color: 'error', label: '错误' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', label: status || '未知' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => `$${balance.toLocaleString()}`,
    },
    {
      title: '最后同步',
      dataIndex: 'lastSync',
      key: 'lastSync',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: ExchangeAccount) => (
        <Space>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingAccount(record);
              accountForm.setFieldsValue(record);
              setAccountModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个账户吗？"
            onConfirm={() => handleDeleteAccount(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>系统设置</Title>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={fetchSettings}>
            重新加载
          </Button>
          <Button onClick={handleResetSettings}>
            重置默认
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => form.submit()}
            loading={loading}
          >
            保存设置
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveSettings}
        initialValues={settings}
      >
        {/* 交易设置 */}
        <Card title="交易设置" style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['trading', 'maxOpenTrades']}
                label="最大同时开仓数"
                rules={[{ required: true, message: '请输入最大开仓数' }]}
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['trading', 'stakeAmount']}
                label="单次下单金额"
                rules={[{ required: true, message: '请输入下单金额' }]}
              >
                <InputNumber min={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['trading', 'stakeCurrency']}
                label="计价货币"
                rules={[{ required: true, message: '请选择计价货币' }]}
              >
                <Select>
                  <Option value="USDT">USDT</Option>
                  <Option value="BTC">BTC</Option>
                  <Option value="ETH">ETH</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['trading', 'dryRun']}
                label="模拟交易模式"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['trading', 'enableTelegramBot']}
                label="启用Telegram机器人"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['trading', 'telegramToken']}
                label="Telegram Bot Token"
              >
                <Input.Password placeholder="请输入Telegram Bot Token" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['trading', 'telegramChatId']}
                label="Telegram Chat ID"
              >
                <Input placeholder="请输入Telegram Chat ID" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 风险管理 */}
        <Card title="风险管理" style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['risk', 'maxDailyLoss']}
                label="每日最大亏损 (%)"
                rules={[{ required: true, message: '请输入每日最大亏损' }]}
              >
                <InputNumber min={0} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['risk', 'maxWeeklyLoss']}
                label="每周最大亏损 (%)"
                rules={[{ required: true, message: '请输入每周最大亏损' }]}
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['risk', 'stopLossEnabled']}
                label="启用止损"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['risk', 'trailingStopEnabled']}
                label="启用跟踪止损"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['risk', 'emergencyStopEnabled']}
                label="启用紧急停止"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* API设置 */}
        <Card title="API设置" style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name={['api', 'enableApi']}
                label="启用API"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item
                name={['api', 'apiUsername']}
                label="API用户名"
                rules={[{ required: true, message: '请输入API用户名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item
                name={['api', 'apiPassword']}
                label="API密码"
                rules={[{ required: true, message: '请输入API密码' }]}
              >
                <Input.Password placeholder="不修改请留空" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['api', 'rateLimitEnabled']}
                label="启用速率限制"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['api', 'rateLimitRequests']}
                label="每分钟请求限制"
              >
                <InputNumber min={10} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['api', 'rateLimitWindow']}
                label="限制窗口 (秒)"
              >
                <InputNumber min={10} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 通知设置 */}
        <Card title="通知设置" style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['notifications', 'emailEnabled']}
                label="启用邮件通知"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['notifications', 'webhookEnabled']}
                label="启用Webhook通知"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['notifications', 'emailSmtpServer']}
                label="SMTP服务器"
              >
                <Input placeholder="smtp.gmail.com" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['notifications', 'webhookUrl']}
                label="Webhook URL"
              >
                <Input placeholder="https://hooks.slack.com/..." />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 性能设置 */}
        <Card title="性能设置" style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['performance', 'enableCaching']}
                label="启用缓存"
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['performance', 'cacheTimeout']}
                label="缓存超时 (秒)"
              >
                <InputNumber min={60} max={3600} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['performance', 'logLevel']}
                label="日志级别"
              >
                <Select>
                  <Option value="DEBUG">DEBUG</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="WARNING">WARNING</Option>
                  <Option value="ERROR">ERROR</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>

      {/* 交易所账户管理 */}
      <Card 
        title="交易所账户" 
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingAccount(null);
              accountForm.resetFields();
              setAccountModalVisible(true);
            }}
          >
            添加账户
          </Button>
        }
      >
        <Table
          columns={exchangeColumns}
          dataSource={exchangeAccounts}
          rowKey="id"
          pagination={false}
        />
      </Card>

      {/* 添加/编辑账户模态框 */}
      <Modal
        title={editingAccount ? '编辑交易所账户' : '添加交易所账户'}
        open={accountModalVisible}
        onCancel={() => {
          setAccountModalVisible(false);
          accountForm.resetFields();
          setEditingAccount(null);
        }}
        onOk={() => accountForm.submit()}
        width={600}
      >
        <Form
          form={accountForm}
          layout="vertical"
          onFinish={handleSaveExchangeAccount}
        >
          <Form.Item
            name="name"
            label="账户名称"
            rules={[{ required: true, message: '请输入账户名称' }]}
          >
            <Input placeholder="例如：Binance主账户" />
          </Form.Item>

          <Form.Item
            name="exchange"
            label="交易所"
            rules={[{ required: true, message: '请选择交易所' }]}
          >
            <Select placeholder="请选择交易所">
              <Option value="binance">Binance</Option>
              <Option value="okx">OKX</Option>
              <Option value="huobi">Huobi</Option>
              <Option value="bybit">Bybit</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="apiKey"
            label="API Key"
            rules={[{ required: true, message: '请输入API Key' }]}
          >
            <Input.Password placeholder="请输入API Key" />
          </Form.Item>

          <Form.Item
            name="apiSecret"
            label="API Secret"
            rules={[{ required: true, message: '请输入API Secret' }]}
          >
            <Input.Password placeholder="请输入API Secret" />
          </Form.Item>

          <Form.Item
            name="passphrase"
            label="Passphrase"
            help="部分交易所需要"
          >
            <Input.Password placeholder="请输入Passphrase（如需要）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Settings; 