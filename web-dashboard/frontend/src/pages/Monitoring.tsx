import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Space,
  Tag,
  Alert,
  Table,
  Button,
  Descriptions,
  Timeline,
  Badge
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import axios from 'axios';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface SystemStatus {
  freqtradeStatus: 'running' | 'stopped' | 'error';
  apiStatus: 'healthy' | 'unhealthy';
  dbStatus: 'connected' | 'disconnected';
  uptime: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  activeStrategies: number;
  openTrades: number;
  lastUpdate: string;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  source: string;
}

interface PerformanceMetric {
  timestamp: string;
  cpu: number;
  memory: number;
  trades: number;
}

const Monitoring: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    freqtradeStatus: 'running',
    apiStatus: 'healthy',
    dbStatus: 'connected',
    uptime: '2天 5小时 32分钟',
    cpuUsage: 25.6,
    memoryUsage: 45.2,
    diskUsage: 68.7,
    activeStrategies: 2,
    openTrades: 8,
    lastUpdate: dayjs().format('YYYY-MM-DD HH:mm:ss')
  });
  
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [performanceData, setPerformanceData] = useState<PerformanceMetric[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSystemStatus();
    fetchLogs();
    fetchPerformanceData();
    
    // 每30秒更新一次
    const interval = setInterval(() => {
      fetchSystemStatus();
      fetchPerformanceData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchSystemStatus = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/status');
      setSystemStatus(response.data);
    } catch (error) {
      // 模拟数据已在state中设置
    }
  };

  const fetchLogs = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/logs');
      setLogs(response.data);
    } catch (error) {
      // 模拟数据
      setLogs([
        {
          id: '1',
          timestamp: dayjs().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          level: 'info',
          message: '策略 Model3Strategy 执行买入信号',
          source: 'strategy'
        },
        {
          id: '2',
          timestamp: dayjs().subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          level: 'warning',
          message: 'API调用频率接近限制',
          source: 'api'
        },
        {
          id: '3',
          timestamp: dayjs().subtract(15, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          level: 'info',
          message: '系统状态检查完成',
          source: 'system'
        },
        {
          id: '4',
          timestamp: dayjs().subtract(20, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          level: 'error',
          message: '网络连接超时，正在重试',
          source: 'network'
        }
      ]);
    }
  };

  const fetchPerformanceData = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/performance');
      setPerformanceData(response.data);
    } catch (error) {
      // 模拟数据
      const data = Array.from({ length: 24 }, (_, i) => ({
        timestamp: dayjs().subtract(23 - i, 'hour').format('HH:mm'),
        cpu: 20 + Math.random() * 30,
        memory: 40 + Math.random() * 20,
        trades: Math.floor(Math.random() * 10)
      }));
      setPerformanceData(data);
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    Promise.all([
      fetchSystemStatus(),
      fetchLogs(),
      fetchPerformanceData()
    ]).finally(() => {
      setLoading(false);
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
      case 'healthy':
      case 'connected':
        return 'success';
      case 'warning':
        return 'warning';
      case 'stopped':
      case 'unhealthy':
      case 'disconnected':
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
      case 'healthy':
      case 'connected':
        return <CheckCircleOutlined />;
      case 'warning':
        return <ExclamationCircleOutlined />;
      case 'stopped':
      case 'unhealthy':
      case 'disconnected':
      case 'error':
        return <CloseCircleOutlined />;
      default:
        return null;
    }
  };

  const getCpuMemoryChartOption = () => {
    return {
      title: {
        text: 'CPU和内存使用率',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['CPU使用率', '内存使用率'],
        bottom: 10
      },
      xAxis: {
        type: 'category',
        data: performanceData.map(d => d.timestamp)
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: 'CPU使用率',
          type: 'line',
          data: performanceData.map(d => d.cpu.toFixed(1)),
          smooth: true,
          lineStyle: {
            color: '#1890ff'
          }
        },
        {
          name: '内存使用率',
          type: 'line',
          data: performanceData.map(d => d.memory.toFixed(1)),
          smooth: true,
          lineStyle: {
            color: '#52c41a'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      }
    };
  };

  const getTradesChartOption = () => {
    return {
      title: {
        text: '每小时交易次数',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: performanceData.map(d => d.timestamp)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: performanceData.map(d => d.trades),
        type: 'bar',
        itemStyle: {
          color: '#faad14'
        }
      }],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };
  };

  const logColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: string) => {
        const levelMap = {
          info: { color: 'blue', label: '信息' },
          warning: { color: 'orange', label: '警告' },
          error: { color: 'red', label: '错误' }
        };
        const levelInfo = levelMap[level as keyof typeof levelMap];
        return <Tag color={levelInfo.color}>{levelInfo.label}</Tag>;
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 100,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>系统监控</Title>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
            刷新
          </Button>
          <Button icon={<SettingOutlined />}>
            设置
          </Button>
        </Space>
      </div>

      {/* 系统状态概览 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Freqtrade状态"
              value={systemStatus.freqtradeStatus === 'running' ? '运行中' : '已停止'}
              prefix={getStatusIcon(systemStatus.freqtradeStatus)}
              valueStyle={{ color: getStatusColor(systemStatus.freqtradeStatus) === 'success' ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="API状态"
              value={systemStatus.apiStatus === 'healthy' ? '健康' : '异常'}
              prefix={getStatusIcon(systemStatus.apiStatus)}
              valueStyle={{ color: getStatusColor(systemStatus.apiStatus) === 'success' ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据库状态"
              value={systemStatus.dbStatus === 'connected' ? '已连接' : '断开'}
              prefix={getStatusIcon(systemStatus.dbStatus)}
              valueStyle={{ color: getStatusColor(systemStatus.dbStatus) === 'success' ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统运行时间"
              value={systemStatus.uptime}
            />
          </Card>
        </Col>
      </Row>

      {/* 资源使用情况 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card title="CPU使用率">
            <Progress
              type="dashboard"
              percent={systemStatus.cpuUsage}
              status={systemStatus.cpuUsage > 80 ? 'exception' : 'normal'}
              format={percent => `${percent}%`}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="内存使用率">
            <Progress
              type="dashboard"
              percent={systemStatus.memoryUsage}
              status={systemStatus.memoryUsage > 80 ? 'exception' : 'normal'}
              format={percent => `${percent}%`}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="磁盘使用率">
            <Progress
              type="dashboard"
              percent={systemStatus.diskUsage}
              status={systemStatus.diskUsage > 90 ? 'exception' : 'normal'}
              format={percent => `${percent}%`}
            />
          </Card>
        </Col>
      </Row>

      {/* 交易状态 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card title="交易状态">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic title="活跃策略" value={systemStatus.activeStrategies} />
              </Col>
              <Col span={12}>
                <Statistic title="开仓交易" value={systemStatus.openTrades} />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="系统信息">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="最后更新">{systemStatus.lastUpdate}</Descriptions.Item>
              <Descriptions.Item label="版本">Freqtrade v2024.1</Descriptions.Item>
              <Descriptions.Item label="环境">生产环境</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* 性能图表 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card title="资源使用趋势">
            <ReactECharts option={getCpuMemoryChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="交易活动">
            <ReactECharts option={getTradesChartOption()} style={{ height: '300px' }} />
          </Card>
        </Col>
      </Row>

      {/* 系统日志 */}
      <Card title="系统日志">
        <Table
          columns={logColumns}
          dataSource={logs}
          rowKey="id"
          size="small"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条日志`
          }}
        />
      </Card>

      {/* 告警信息 */}
      {systemStatus.cpuUsage > 80 && (
        <Alert
          message="CPU使用率过高"
          description="当前CPU使用率超过80%，请检查系统负载"
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}
      
      {systemStatus.memoryUsage > 80 && (
        <Alert
          message="内存使用率过高"
          description="当前内存使用率超过80%，建议清理缓存或重启服务"
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}
    </div>
  );
};

export default Monitoring; 