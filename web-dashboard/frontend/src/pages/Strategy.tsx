import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  message,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Progress,
  Descriptions
} from 'antd';
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface Strategy {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'backtesting';
  type: 'technical' | 'fundamental' | 'arbitrage' | 'custom';
  performance: {
    totalReturn: number;
    sharpeRatio: number;
    maxDrawdown: number;
    winRate: number;
  };
  config: {
    timeframe: string;
    minRoi: number;
    stoploss: number;
    trailing: boolean;
  };
  createdAt: string;
  lastUpdated: string;
}

const Strategy: React.FC = () => {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/strategies');
      setStrategies(response.data);
    } catch (error) {
      message.error('获取策略列表失败');
      // 模拟数据
      setStrategies([
        {
          id: '1',
          name: 'Model3Strategy',
          description: '基于技术指标的简单套利策略',
          status: 'active',
          type: 'arbitrage',
          performance: {
            totalReturn: 12.5,
            sharpeRatio: 1.8,
            maxDrawdown: -5.2,
            winRate: 68.5
          },
          config: {
            timeframe: '5m',
            minRoi: 0.02,
            stoploss: -0.05,
            trailing: true
          },
          createdAt: '2024-01-15',
          lastUpdated: '2024-01-20'
        },
        {
          id: '2',
          name: 'EMACrossStrategy',
          description: 'EMA均线交叉策略',
          status: 'inactive',
          type: 'technical',
          performance: {
            totalReturn: 8.3,
            sharpeRatio: 1.2,
            maxDrawdown: -8.1,
            winRate: 55.2
          },
          config: {
            timeframe: '15m',
            minRoi: 0.015,
            stoploss: -0.08,
            trailing: false
          },
          createdAt: '2024-01-10',
          lastUpdated: '2024-01-18'
        }
      ]);
    }
    setLoading(false);
  };

  const handleStatusChange = async (strategyId: string, newStatus: string) => {
    try {
      await axios.put(`/api/v1/strategies/${strategyId}/status`, { status: newStatus });
      message.success('策略状态更新成功');
      fetchStrategies();
    } catch (error) {
      message.error('更新策略状态失败');
    }
  };

  const handleEdit = (strategy: Strategy) => {
    setEditingStrategy(strategy);
    form.setFieldsValue(strategy);
    setModalVisible(true);
  };

  const handleDelete = async (strategyId: string) => {
    try {
      await axios.delete(`/api/v1/strategies/${strategyId}`);
      message.success('删除策略成功');
      fetchStrategies();
    } catch (error) {
      message.error('删除策略失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingStrategy) {
        await axios.put(`/api/v1/strategies/${editingStrategy.id}`, values);
        message.success('更新策略成功');
      } else {
        await axios.post('/api/v1/strategies', values);
        message.success('创建策略成功');
      }
      setModalVisible(false);
      form.resetFields();
      setEditingStrategy(null);
      fetchStrategies();
    } catch (error) {
      message.error(editingStrategy ? '更新策略失败' : '创建策略失败');
    }
  };

  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Strategy) => (
        <Space direction="vertical" size={0}>
          <Text strong>{text}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          technical: { color: 'blue', label: '技术分析' },
          fundamental: { color: 'green', label: '基本面' },
          arbitrage: { color: 'orange', label: '套利' },
          custom: { color: 'purple', label: '自定义' }
        };
        const typeInfo = typeMap[type as keyof typeof typeMap];
        return <Tag color={typeInfo.color}>{typeInfo.label}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: Strategy) => {
        const statusMap = {
          active: { color: 'success', label: '运行中' },
          inactive: { color: 'default', label: '已停止' },
          backtesting: { color: 'processing', label: '回测中' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', label: status || '未知' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '收益率',
      dataIndex: ['performance', 'totalReturn'],
      key: 'totalReturn',
      render: (value: number) => (
        <Text type={value >= 0 ? 'success' : 'danger'}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      ),
    },
    {
      title: '胜率',
      dataIndex: ['performance', 'winRate'],
      key: 'winRate',
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          status={value >= 60 ? 'success' : value >= 40 ? 'normal' : 'exception'}
          format={percent => `${percent}%`}
        />
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Strategy) => (
        <Space>
          {record.status === 'active' ? (
            <Button
              size="small"
              icon={<PauseCircleOutlined />}
              onClick={() => handleStatusChange(record.id, 'inactive')}
            >
              停止
            </Button>
          ) : (
            <Button
              size="small"
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStatusChange(record.id, 'active')}
            >
              启动
            </Button>
          )}
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            size="small"
            icon={<BarChartOutlined />}
            onClick={() => message.info('查看详细性能分析')}
          >
            分析
          </Button>
          <Popconfirm
            title="确定要删除这个策略吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const activeStrategies = strategies.filter(s => s.status === 'active').length;
  const totalReturn = strategies.reduce((sum, s) => sum + s.performance.totalReturn, 0) / strategies.length || 0;

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>策略管理</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总策略数" value={strategies.length} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="运行中策略" value={activeStrategies} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均收益率"
              value={totalReturn}
              precision={2}
              suffix="%"
              valueStyle={{ color: totalReturn >= 0 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="策略活跃度" value={85} suffix="%" />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <Space style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingStrategy(null);
            form.resetFields();
            setModalVisible(true);
          }}
        >
          新建策略
        </Button>
        <Upload>
          <Button icon={<UploadOutlined />}>导入策略</Button>
        </Upload>
        <Button icon={<DownloadOutlined />}>导出策略</Button>
      </Space>

      {/* 策略列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={strategies}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 编辑/新建策略模态框 */}
      <Modal
        title={editingStrategy ? '编辑策略' : '新建策略'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingStrategy(null);
        }}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="策略名称"
                rules={[{ required: true, message: '请输入策略名称' }]}
              >
                <Input placeholder="请输入策略名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="策略类型"
                rules={[{ required: true, message: '请选择策略类型' }]}
              >
                <Select placeholder="请选择策略类型">
                  <Option value="technical">技术分析</Option>
                  <Option value="fundamental">基本面</Option>
                  <Option value="arbitrage">套利</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="策略描述"
            rules={[{ required: true, message: '请输入策略描述' }]}
          >
            <TextArea rows={3} placeholder="请输入策略描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['config', 'timeframe']}
                label="时间周期"
                rules={[{ required: true, message: '请选择时间周期' }]}
              >
                <Select placeholder="请选择时间周期">
                  <Option value="1m">1分钟</Option>
                  <Option value="5m">5分钟</Option>
                  <Option value="15m">15分钟</Option>
                  <Option value="1h">1小时</Option>
                  <Option value="4h">4小时</Option>
                  <Option value="1d">1天</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['config', 'minRoi']}
                label="最小收益率"
                rules={[{ required: true, message: '请输入最小收益率' }]}
              >
                <Input type="number" step="0.001" placeholder="0.02" suffix="%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['config', 'stoploss']}
                label="止损比例"
                rules={[{ required: true, message: '请输入止损比例' }]}
              >
                <Input type="number" step="0.001" placeholder="-0.05" suffix="%" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default Strategy; 