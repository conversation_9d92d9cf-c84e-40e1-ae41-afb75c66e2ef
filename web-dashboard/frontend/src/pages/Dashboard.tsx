import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Tag,
  Table,
  <PERSON><PERSON>,
  <PERSON>ert,
  Badge,
  Tooltip,
  notification
} from 'antd';
import {
  DollarCircleOutlined,
  RiseOutlined,
  FallOutlined,
  TrophyOutlined,
  SwapOutlined,
  ReloadOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { websocketService } from '../services/websocket';

const { Title, Text } = Typography;

interface DashboardData {
  status: string;
  total_profit: number;
  total_profit_percentage: number;
  open_trades: number;
  closed_trades: number;
  win_rate: number;
  balance?: any;
  last_update: string;
}

interface Trade {
  id: string;
  pair: string;
  side: string;
  amount: number;
  price: number;
  profit: number;
  profit_percentage: number;
  timestamp: string;
  status: string;
}

interface RealtimeMetrics {
  totalProfit: number;
  profitPercentage: number;
  openTrades: number;
  winRate: number;
  timestamp: string;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [realtimeMetrics, setRealtimeMetrics] = useState<RealtimeMetrics[]>([]);
  const [notifications, setNotifications] = useState<any[]>([]);

  // WebSocket实时数据处理
  const handleDashboardUpdate = useCallback((data: any) => {
    console.log('📊 仪表板实时数据更新:', data);
    
    // 处理trading_metrics数据结构
    const metrics = data.metrics || {};
    const profit = data.profit || {};
    
    // 构造仪表板数据
    const dashboardData: DashboardData = {
      status: 'running',
      total_profit: profit.total_profit_abs || 0,
      total_profit_percentage: profit.total_profit_ratio || 0,
      open_trades: metrics.open_trades || 0,
      closed_trades: metrics.closed_trades || 0,
      win_rate: metrics.win_rate || 0,
      balance: data.balance,
      last_update: data.timestamp || new Date().toISOString()
    };
    
    setData(dashboardData);
    
    // 更新实时指标历史数据
    const newMetric: RealtimeMetrics = {
      totalProfit: dashboardData.total_profit,
      profitPercentage: dashboardData.total_profit_percentage,
      openTrades: dashboardData.open_trades,
      winRate: dashboardData.win_rate,
      timestamp: dashboardData.last_update
    };
    
    setRealtimeMetrics(prev => {
      const updated = [...prev, newMetric];
      // 只保留最近50个数据点
      return updated.slice(-50);
    });
  }, []);

  const handleTradingStatus = useCallback((data: any) => {
    console.log('💹 交易状态更新:', data);
    if (data.recent_trades) {
      setTrades(data.recent_trades);
    }
  }, []);

  const handleSystemStatus = useCallback((data: any) => {
    console.log('🖥️ 系统状态更新:', data);
    setConnectionStatus(data.connected ? 'connected' : 'disconnected');
  }, []);

  const handleNotification = useCallback((data: any) => {
    console.log('📢 收到通知:', data);
    
    // 显示Ant Design通知
    const notificationType = data.level === 'error' ? 'error' : 
                           data.level === 'warning' ? 'warning' : 'info';
    
    notification[notificationType]({
      message: '系统通知',
      description: data.message,
      duration: 5,
      placement: 'topRight'
    });
    
    // 添加到通知列表
    setNotifications(prev => [...prev.slice(-9), data]); // 只保留最近10条
  }, []);

  // 初始化WebSocket连接
  useEffect(() => {
    const initWebSocket = async () => {
      try {
        setConnectionStatus('connecting');
        console.log('🔌 初始化WebSocket连接...');
        
        await websocketService.connect();
        setConnectionStatus('connected');
        
        // 订阅实时数据
        websocketService.subscribe('trading_metrics', handleDashboardUpdate);
        websocketService.subscribe('trading_status', handleTradingStatus);
        websocketService.subscribe('system_status', handleSystemStatus);
        websocketService.subscribe('notifications', handleNotification);
        
        console.log('✅ WebSocket订阅完成');
        
      } catch (error) {
        console.error('❌ WebSocket连接失败:', error);
        setConnectionStatus('disconnected');
        setError('WebSocket连接失败，将使用轮询方式获取数据');
      }
    };

    initWebSocket();

    // 组件卸载时清理
    return () => {
      console.log('🧹 清理WebSocket连接');
      websocketService.unsubscribe('trading_metrics', handleDashboardUpdate);
      websocketService.unsubscribe('trading_status', handleTradingStatus);
      websocketService.unsubscribe('system_status', handleSystemStatus);
      websocketService.unsubscribe('notifications', handleNotification);
      websocketService.disconnect();
    };
  }, [handleDashboardUpdate, handleTradingStatus, handleSystemStatus, handleNotification]);

  // 传统HTTP API获取数据（作为WebSocket的备用方案）
  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      // 获取概览数据
      const overviewResponse = await fetch('http://localhost:8081/api/v1/dashboard/overview');
      if (!overviewResponse.ok) {
        throw new Error('获取概览数据失败');
      }
      const overviewData = await overviewResponse.json();
      setData(overviewData);

      // 获取最近交易
      const tradesResponse = await fetch('http://localhost:8081/api/v1/dashboard/trades/recent?limit=10');
      if (!tradesResponse.ok) {
        throw new Error('获取交易数据失败');
      }
      const tradesData = await tradesResponse.json();
      setTrades(tradesData.trades || []);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始数据加载
  useEffect(() => {
    fetchData();
  }, []);

  // 手动刷新数据
  const handleRefresh = () => {
    if (connectionStatus === 'connected') {
      // 如果WebSocket连接正常，发送心跳
      websocketService.ping();
    } else {
      // 否则使用HTTP API刷新
      fetchData();
    }
  };

  // 实时利润图表配置
  const getProfitChartOption = () => {
    const timeData = realtimeMetrics.map(m => 
      new Date(m.timestamp).toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    );
    const profitData = realtimeMetrics.map(m => m.totalProfit);
    const percentageData = realtimeMetrics.map(m => m.profitPercentage);

    return {
      title: {
        text: '实时收益趋势',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: ['总收益', '收益率'],
        bottom: 0
      },
      grid: {
        top: 40,
        bottom: 50,
        left: 50,
        right: 50
      },
      xAxis: {
        type: 'category',
        data: timeData,
        axisLabel: { rotate: 45 }
      },
      yAxis: [
        {
          type: 'value',
          name: '总收益 (USDT)',
          position: 'left'
        },
        {
          type: 'value',
          name: '收益率 (%)',
          position: 'right'
        }
      ],
      series: [
        {
          name: '总收益',
          type: 'line',
          data: profitData,
          yAxisIndex: 0,
          itemStyle: { color: '#52c41a' },
          smooth: true
        },
        {
          name: '收益率',
          type: 'line',
          data: percentageData,
          yAxisIndex: 1,
          itemStyle: { color: '#1890ff' },
          smooth: true
        }
      ]
    };
  };

  // 交易表格列配置
  const columns = [
    {
      title: '交易对',
      dataIndex: 'pair',
      key: 'pair',
      width: 100,
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      width: 80,
      render: (side: string) => (
        <Tag color={side === 'buy' ? 'green' : 'red'}>
          {side === 'buy' ? '买入' : '卖出'}
        </Tag>
      ),
    },
    {
      title: '数量',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      render: (amount: number) => amount?.toFixed(6) || '-',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price: number) => price?.toFixed(4) || '-',
    },
    {
      title: '收益',
      dataIndex: 'profit',
      key: 'profit',
      width: 100,
      render: (profit: number) => (
        <span style={{ color: profit >= 0 ? '#52c41a' : '#ff4d4f' }}>
          {profit >= 0 ? '+' : ''}{profit?.toFixed(4) || '0.0000'}
        </span>
      ),
    },
    {
      title: '收益率',
      dataIndex: 'profit_percentage',
      key: 'profit_percentage',
      width: 100,
      render: (percentage: number) => (
        <span style={{ color: percentage >= 0 ? '#52c41a' : '#ff4d4f' }}>
          {percentage >= 0 ? '+' : ''}{percentage?.toFixed(2) || '0.00'}%
        </span>
      ),
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 120,
      render: (timestamp: string) => 
        timestamp ? new Date(timestamp).toLocaleString() : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'open' ? 'blue' : 'green'}>
          {status === 'open' ? '开仓' : '平仓'}
        </Tag>
      ),
    },
  ];

  if (loading && !data) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Title level={4}>正在加载仪表板数据...</Title>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>实时交易仪表板</Title>
        <Space>
          <Tooltip title={`WebSocket状态: ${connectionStatus}`}>
            <Badge 
              status={connectionStatus === 'connected' ? 'success' : 'error'} 
              text={connectionStatus === 'connected' ? '实时连接' : '离线模式'}
            />
          </Tooltip>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
            size="small"
          >
            刷新
          </Button>
        </Space>
      </div>

      {error && (
        <Alert
          message="数据获取警告"
          description={error}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
          closable
        />
      )}

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收益"
              value={data?.total_profit || 0}
              precision={4}
              valueStyle={{ 
                color: (data?.total_profit || 0) >= 0 ? '#3f8600' : '#cf1322' 
              }}
              prefix={<DollarCircleOutlined />}
              suffix="USDT"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="收益率"
              value={data?.total_profit_percentage || 0}
              precision={2}
              valueStyle={{ 
                color: (data?.total_profit_percentage || 0) >= 0 ? '#3f8600' : '#cf1322' 
              }}
              prefix={
                (data?.total_profit_percentage || 0) >= 0 ? 
                <RiseOutlined /> : <FallOutlined />
              }
              suffix="%"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="开仓交易"
              value={data?.open_trades || 0}
              prefix={<SwapOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="胜率"
              value={data?.win_rate || 0}
              precision={1}
              prefix={<TrophyOutlined />}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 实时图表 */}
      {realtimeMetrics.length > 0 && (
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card title="实时收益趋势图" size="small">
              <ReactECharts
                option={getProfitChartOption()}
                style={{ height: '300px' }}
                opts={{ renderer: 'canvas' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 系统状态 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card
            title="系统状态"
            extra={
              <Space>
                <Badge 
                  status={connectionStatus === 'connected' ? 'processing' : 'default'}
                  text={connectionStatus === 'connected' ? 'WebSocket连接' : 'HTTP轮询'}
                />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                  size="small"
                >
                  刷新
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <ApiOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                  <span>Freqtrade状态: </span>
                  <Tag color={data?.status === 'running' ? 'green' : 'red'}>
                    {data?.status === 'running' ? '运行中' : '离线'}
                  </Tag>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ marginBottom: 8 }}>
                  <span>已完成交易: {data?.closed_trades || 0}</span>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ marginBottom: 8 }}>
                  <span>最后更新: {data?.last_update ? new Date(data.last_update).toLocaleString() : '-'}</span>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 最近交易 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <span>最近交易</span>
                {connectionStatus === 'connected' && (
                  <Badge status="processing" text="实时更新" />
                )}
              </Space>
            }
          >
            <Table
              columns={columns}
              dataSource={trades}
              rowKey="id"
              pagination={false}
              scroll={{ x: 800 }}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard; 