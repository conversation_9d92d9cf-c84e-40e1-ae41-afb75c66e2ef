{"name": "freqtrade-web-dashboard", "version": "1.0.0", "description": "Freqtrade量化交易系统Web管理界面", "private": true, "dependencies": {"@ant-design/plots": "^2.6.0", "@tanstack/react-query": "^5.8.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.202", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "socket.io-client": "^4.7.4", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^24.0.10", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0"}, "proxy": "http://localhost:8081"}