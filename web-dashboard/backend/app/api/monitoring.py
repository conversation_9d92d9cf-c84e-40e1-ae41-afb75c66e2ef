"""
监控API路由
提供系统监控和告警功能
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
from app.services.monitoring_service import MonitoringService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# 依赖注入
def get_monitoring_service() -> MonitoringService:
    return MonitoringService()

@router.get("/status")
async def get_system_status(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取系统状态概览"""
    try:
        status = await monitoring_service.get_system_status()
        return status
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")

@router.get("/resources")
async def get_resource_usage(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取资源使用情况"""
    try:
        resources = await monitoring_service.get_resource_usage()
        return resources
    except Exception as e:
        logger.error(f"获取资源使用情况失败: {e}")
        raise HTTPException(status_code=500, detail="获取资源使用情况失败")

@router.get("/performance")
async def get_performance_metrics(
    hours: int = 24,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取性能指标趋势"""
    try:
        metrics = await monitoring_service.get_performance_metrics(hours)
        return metrics
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail="获取性能指标失败")

@router.get("/logs")
async def get_system_logs(
    level: str = "all",
    limit: int = 100,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取系统日志"""
    try:
        if level not in ["all", "info", "warning", "error"]:
            raise HTTPException(status_code=400, detail="无效的日志级别")
        
        logs = await monitoring_service.get_system_logs(level, limit)
        return logs
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统日志失败")

@router.get("/alerts")
async def get_active_alerts(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取活跃告警"""
    try:
        alerts = await monitoring_service.get_active_alerts()
        return alerts
    except Exception as e:
        logger.error(f"获取活跃告警失败: {e}")
        raise HTTPException(status_code=500, detail="获取活跃告警失败")

@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: str,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """确认告警"""
    try:
        success = await monitoring_service.acknowledge_alert(alert_id)
        if not success:
            raise HTTPException(status_code=404, detail="告警不存在")
        
        return {"message": "告警已确认", "alert_id": alert_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认告警失败: {e}")
        raise HTTPException(status_code=500, detail="确认告警失败")

@router.get("/health")
async def get_health_check(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """健康检查"""
    try:
        health = await monitoring_service.get_health_check()
        return health
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")

@router.get("/freqtrade/status")
async def get_freqtrade_status(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取Freqtrade状态"""
    try:
        status = await monitoring_service.get_freqtrade_status()
        return status
    except Exception as e:
        logger.error(f"获取Freqtrade状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取Freqtrade状态失败")

@router.get("/database/status")
async def get_database_status(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取数据库状态"""
    try:
        status = await monitoring_service.get_database_status()
        return status
    except Exception as e:
        logger.error(f"获取数据库状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据库状态失败")

@router.get("/trading/stats")
async def get_trading_stats(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """获取交易统计"""
    try:
        stats = await monitoring_service.get_trading_stats()
        return stats
    except Exception as e:
        logger.error(f"获取交易统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取交易统计失败") 