from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from app.schemas.strategy import StrategyResponse, StrategyCreate, StrategyUpdate
from app.services.strategy_service import StrategyService
from app.core.timeframe_config import TimeframeConfig
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# 依赖注入
def get_strategy_service() -> StrategyService:
    return StrategyService()

@router.get("/", response_model=List[StrategyResponse])
async def get_strategies(
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """获取所有策略列表"""
    try:
        strategies = await strategy_service.get_all_strategies()
        return strategies
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略列表失败")

@router.get("/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: str,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """获取单个策略详情"""
    try:
        strategy = await strategy_service.get_strategy_by_id(strategy_id)
        if not strategy:
            raise HTTPException(status_code=404, detail="策略不存在")
        return strategy
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略详情失败")

@router.post("/", response_model=StrategyResponse)
async def create_strategy(
    strategy: StrategyCreate,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """创建新策略"""
    try:
        new_strategy = await strategy_service.create_strategy(strategy)
        return new_strategy
    except Exception as e:
        logger.error(f"创建策略失败: {e}")
        raise HTTPException(status_code=500, detail="创建策略失败")

@router.put("/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: str,
    strategy_update: StrategyUpdate,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """更新策略"""
    try:
        updated_strategy = await strategy_service.update_strategy(strategy_id, strategy_update)
        if not updated_strategy:
            raise HTTPException(status_code=404, detail="策略不存在")
        return updated_strategy
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新策略失败: {e}")
        raise HTTPException(status_code=500, detail="更新策略失败")

@router.put("/{strategy_id}/status")
async def update_strategy_status(
    strategy_id: str,
    status_data: dict,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """更新策略状态"""
    try:
        status = status_data.get("status")
        if not status:
            raise HTTPException(status_code=400, detail="缺少状态参数")
        
        success = await strategy_service.update_strategy_status(strategy_id, status)
        if not success:
            raise HTTPException(status_code=404, detail="策略不存在")
        
        return {"message": "策略状态更新成功", "strategy_id": strategy_id, "status": status}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新策略状态失败: {e}")
        raise HTTPException(status_code=500, detail="更新策略状态失败")

@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: str,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """删除策略"""
    try:
        success = await strategy_service.delete_strategy(strategy_id)
        if not success:
            raise HTTPException(status_code=404, detail="策略不存在")
        
        return {"message": "策略删除成功", "strategy_id": strategy_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除策略失败: {e}")
        raise HTTPException(status_code=500, detail="删除策略失败")

@router.get("/{strategy_id}/performance")
async def get_strategy_performance(
    strategy_id: str,
    days: Optional[int] = 30,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """获取策略性能数据"""
    try:
        performance = await strategy_service.get_strategy_performance(strategy_id, days)
        if not performance:
            raise HTTPException(status_code=404, detail="策略不存在或无性能数据")
        return performance
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略性能失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略性能失败")

@router.post("/{strategy_id}/backtest")
async def start_strategy_backtest(
    strategy_id: str,
    backtest_config: dict,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """启动策略回测"""
    try:
        backtest_id = await strategy_service.start_backtest(strategy_id, backtest_config)
        return {
            "message": "回测已启动",
            "backtest_id": backtest_id,
            "strategy_id": strategy_id
        }
    except Exception as e:
        logger.error(f"启动回测失败: {e}")
        raise HTTPException(status_code=500, detail="启动回测失败")

@router.get("/{strategy_id}/trades")
async def get_strategy_trades(
    strategy_id: str,
    limit: Optional[int] = 100,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """获取策略交易记录"""
    try:
        trades = await strategy_service.get_strategy_trades(strategy_id, limit)
        return trades
    except Exception as e:
        logger.error(f"获取策略交易记录失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略交易记录失败")

@router.get("/available/")
async def get_available_strategies(
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """获取可用策略列表"""
    try:
        strategies = await strategy_service.get_available_strategies()
        return [{"name": name} for name in strategies]
    except Exception as e:
        logger.error(f"获取可用策略列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取可用策略列表失败")

@router.get("/info/{strategy_name}")
async def get_strategy_info(
    strategy_name: str,
    strategy_service: StrategyService = Depends(get_strategy_service)
):
    """获取策略详细信息"""
    try:
        strategy_info = await strategy_service.get_strategy_info(strategy_name)
        if not strategy_info:
            raise HTTPException(status_code=404, detail="策略不存在")
        return strategy_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略信息失败")

@router.get("/timeframes/all")
async def get_all_timeframes() -> Dict[str, Any]:
    """获取所有时间周期配置信息"""
    try:
        return {
            "timeframes": TimeframeConfig.TIMEFRAMES,
            "categories": {
                "scalping": "超短线 (1m-5m)",
                "short_term": "短线 (15m-1h)",
                "medium_term": "中线 (2h-12h)",
                "long_term": "长线 (1d+)"
            }
        }
    except Exception as e:
        logger.error(f"获取时间周期配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取时间周期配置失败")

@router.get("/timeframes/recommend")
async def get_recommended_timeframes(
    capital: float = 10000,
    risk_tolerance: str = "moderate"
) -> Dict[str, Any]:
    """根据资金和风险偏好推荐时间周期"""
    try:
        recommended = TimeframeConfig.get_recommended_timeframes(capital, risk_tolerance)
        return {
            "recommended_timeframes": recommended,
            "capital": capital,
            "risk_tolerance": risk_tolerance,
            "details": {
                tf: TimeframeConfig.get_timeframe_info(tf)
                for tf in recommended
            }
        }
    except Exception as e:
        logger.error(f"获取推荐时间周期失败: {e}")
        raise HTTPException(status_code=500, detail="获取推荐时间周期失败")

@router.post("/timeframes/validate")
async def validate_timeframe_combination(timeframes: List[str]) -> Dict[str, Any]:
    """验证时间周期组合的合理性"""
    try:
        validation_result = TimeframeConfig.validate_timeframe_combination(timeframes)
        return validation_result
    except Exception as e:
        logger.error(f"验证时间周期组合失败: {e}")
        raise HTTPException(status_code=500, detail="验证时间周期组合失败")