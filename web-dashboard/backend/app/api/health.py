"""
健康检查API路由
提供系统健康状态和版本信息
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import sys
import platform
import psutil
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/")
async def health_check() -> Dict[str, Any]:
    """
    基础健康检查
    返回系统基本状态信息
    """
    try:
        return {
            "status": "healthy",
            "message": "Freqtrade Web Dashboard API 运行正常",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")

@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    详细健康检查
    返回详细的系统状态信息
    """
    try:
        # 获取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 获取内存使用情况
        memory = psutil.virtual_memory()
        
        # 获取磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 获取网络连接数
        connections = len(psutil.net_connections())
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "uptime": psutil.boot_time(),
            "system": {
                "platform": platform.system(),
                "version": platform.version(),
                "architecture": platform.architecture()[0],
                "python_version": sys.version.split()[0]
            },
            "resources": {
                "cpu": {
                    "usage_percent": round(cpu_percent, 2),
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "usage_percent": round(memory.percent, 2)
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "used": disk.used,
                    "usage_percent": round((disk.used / disk.total) * 100, 2)
                },
                "network": {
                    "connections": connections
                }
            }
        }
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="详细健康检查失败")

@router.get("/ping")
async def ping() -> Dict[str, str]:
    """
    简单的ping检查
    用于快速验证API可达性
    """
    return {
        "message": "pong",
        "timestamp": datetime.now().isoformat()
    } 