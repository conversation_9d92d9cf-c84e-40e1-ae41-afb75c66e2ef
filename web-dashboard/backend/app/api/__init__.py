"""
API路由模块
包含所有的API端点定义
"""

# 导出所有API模块
from . import (
    dashboard,
    auth,
    trading,
    strategies,
    backtests,
    monitoring,
    settings,
    health,
    websocket_router
)

from fastapi import APIRouter

# 创建主路由
api_router = APIRouter()

# 包含所有子路由
api_router.include_router(dashboard.router)
api_router.include_router(auth.router)
api_router.include_router(trading.router)
api_router.include_router(strategies.router)
api_router.include_router(backtests.router)
api_router.include_router(monitoring.router)
api_router.include_router(settings.router)
api_router.include_router(health.router)
api_router.include_router(websocket_router.router)

__all__ = [
    "dashboard",
    "auth", 
    "trading",
    "strategies",
    "backtests",
    "monitoring",
    "settings",
    "health",
    "websocket_router",
    "api_router"
] 