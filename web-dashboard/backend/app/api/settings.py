from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
from app.services.settings_service import SettingsService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# 依赖注入
def get_settings_service() -> SettingsService:
    return SettingsService()

@router.get("/")
async def get_all_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取所有系统设置"""
    try:
        settings = await settings_service.get_all_settings()
        return settings
    except Exception as e:
        logger.error(f"获取系统设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统设置失败")

@router.get("/trading")
async def get_trading_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取交易设置"""
    try:
        settings = await settings_service.get_trading_settings()
        return settings
    except Exception as e:
        logger.error(f"获取交易设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取交易设置失败")

@router.put("/trading")
async def update_trading_settings(
    settings: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新交易设置"""
    try:
        updated_settings = await settings_service.update_trading_settings(settings)
        return {
            "message": "交易设置更新成功",
            "settings": updated_settings
        }
    except Exception as e:
        logger.error(f"更新交易设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新交易设置失败")

@router.get("/telegram")
async def get_telegram_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取Telegram设置"""
    try:
        settings = await settings_service.get_telegram_settings()
        return settings
    except Exception as e:
        logger.error(f"获取Telegram设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取Telegram设置失败")

@router.put("/telegram")
async def update_telegram_settings(
    settings: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新Telegram设置"""
    try:
        updated_settings = await settings_service.update_telegram_settings(settings)
        return {
            "message": "Telegram设置更新成功",
            "settings": updated_settings
        }
    except Exception as e:
        logger.error(f"更新Telegram设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新Telegram设置失败")

@router.get("/risk")
async def get_risk_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取风险管理设置"""
    try:
        settings = await settings_service.get_risk_settings()
        return settings
    except Exception as e:
        logger.error(f"获取风险管理设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取风险管理设置失败")

@router.put("/risk")
async def update_risk_settings(
    settings: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新风险管理设置"""
    try:
        updated_settings = await settings_service.update_risk_settings(settings)
        return {
            "message": "风险管理设置更新成功",
            "settings": updated_settings
        }
    except Exception as e:
        logger.error(f"更新风险管理设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新风险管理设置失败")

@router.get("/api")
async def get_api_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取API设置"""
    try:
        settings = await settings_service.get_api_settings()
        return settings
    except Exception as e:
        logger.error(f"获取API设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取API设置失败")

@router.put("/api")
async def update_api_settings(
    settings: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新API设置"""
    try:
        updated_settings = await settings_service.update_api_settings(settings)
        return {
            "message": "API设置更新成功",
            "settings": updated_settings
        }
    except Exception as e:
        logger.error(f"更新API设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新API设置失败")

@router.get("/notifications")
async def get_notification_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取通知设置"""
    try:
        settings = await settings_service.get_notification_settings()
        return settings
    except Exception as e:
        logger.error(f"获取通知设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取通知设置失败")

@router.put("/notifications")
async def update_notification_settings(
    settings: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新通知设置"""
    try:
        updated_settings = await settings_service.update_notification_settings(settings)
        return {
            "message": "通知设置更新成功",
            "settings": updated_settings
        }
    except Exception as e:
        logger.error(f"更新通知设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新通知设置失败")

@router.get("/performance")
async def get_performance_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取性能设置"""
    try:
        settings = await settings_service.get_performance_settings()
        return settings
    except Exception as e:
        logger.error(f"获取性能设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取性能设置失败")

@router.put("/performance")
async def update_performance_settings(
    settings: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新性能设置"""
    try:
        updated_settings = await settings_service.update_performance_settings(settings)
        return {
            "message": "性能设置更新成功",
            "settings": updated_settings
        }
    except Exception as e:
        logger.error(f"更新性能设置失败: {e}")
        raise HTTPException(status_code=500, detail="更新性能设置失败")

@router.get("/exchanges")
async def get_exchange_accounts(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """获取交易所账户列表"""
    try:
        accounts = await settings_service.get_exchange_accounts()
        return accounts
    except Exception as e:
        logger.error(f"获取交易所账户失败: {e}")
        raise HTTPException(status_code=500, detail="获取交易所账户失败")

@router.post("/exchanges")
async def add_exchange_account(
    account: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """添加交易所账户"""
    try:
        new_account = await settings_service.add_exchange_account(account)
        return {
            "message": "交易所账户添加成功",
            "account": new_account
        }
    except Exception as e:
        logger.error(f"添加交易所账户失败: {e}")
        raise HTTPException(status_code=500, detail="添加交易所账户失败")

@router.put("/exchanges/{account_id}")
async def update_exchange_account(
    account_id: str,
    account: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """更新交易所账户"""
    try:
        updated_account = await settings_service.update_exchange_account(account_id, account)
        if not updated_account:
            raise HTTPException(status_code=404, detail="交易所账户不存在")
        
        return {
            "message": "交易所账户更新成功",
            "account": updated_account
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新交易所账户失败: {e}")
        raise HTTPException(status_code=500, detail="更新交易所账户失败")

@router.delete("/exchanges/{account_id}")
async def delete_exchange_account(
    account_id: str,
    settings_service: SettingsService = Depends(get_settings_service)
):
    """删除交易所账户"""
    try:
        success = await settings_service.delete_exchange_account(account_id)
        if not success:
            raise HTTPException(status_code=404, detail="交易所账户不存在")
        
        return {"message": "交易所账户删除成功", "account_id": account_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除交易所账户失败: {e}")
        raise HTTPException(status_code=500, detail="删除交易所账户失败")

@router.post("/test-connection")
async def test_freqtrade_connection(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """测试Freqtrade连接"""
    try:
        result = await settings_service.test_freqtrade_connection()
        return result
    except Exception as e:
        logger.error(f"测试Freqtrade连接失败: {e}")
        raise HTTPException(status_code=500, detail="测试连接失败")

@router.post("/reset/{section}")
async def reset_settings_section(
    section: str,
    settings_service: SettingsService = Depends(get_settings_service)
):
    """重置指定配置节"""
    try:
        if section not in ["trading", "telegram", "risk", "api", "notifications", "performance"]:
            raise HTTPException(status_code=400, detail="无效的配置节")
        
        reset_settings = await settings_service.reset_settings_section(section)
        return {
            "message": f"{section}配置重置成功",
            "settings": reset_settings
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置配置失败: {e}")
        raise HTTPException(status_code=500, detail="重置配置失败")

@router.post("/backup")
async def backup_settings(
    settings_service: SettingsService = Depends(get_settings_service)
):
    """备份所有设置"""
    try:
        backup = await settings_service.backup_settings()
        return {
            "message": "设置备份成功",
            "backup": backup
        }
    except Exception as e:
        logger.error(f"备份设置失败: {e}")
        raise HTTPException(status_code=500, detail="备份设置失败")

@router.post("/restore")
async def restore_settings(
    backup_data: Dict[str, Any],
    settings_service: SettingsService = Depends(get_settings_service)
):
    """恢复设置"""
    try:
        restored_settings = await settings_service.restore_settings(backup_data)
        return {
            "message": "设置恢复成功",
            "settings": restored_settings
        }
    except Exception as e:
        logger.error(f"恢复设置失败: {e}")
        raise HTTPException(status_code=500, detail="恢复设置失败") 