import logging
import os
import uuid
import json
from typing import List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query, Body, Path
from pydantic import BaseModel, Field
from datetime import datetime
import httpx
import asyncio

from app.schemas.backtest import BacktestCreate, BacktestResponse
from app.services.backtest_service import BacktestService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 创建服务实例
backtest_service = BacktestService()

@router.get("/")
async def get_backtests(
    skip: int = 0, 
    limit: int = 100
):
    """
    获取回测列表
    """
    # 添加服务实例ID以便调试
    logger.info(f"获取回测列表，服务实例ID: {id(backtest_service)}")
    try:
        backtests = await backtest_service.get_backtests(skip, limit)
        logger.info(f"返回的回测数量: {len(backtests)}")
        return backtests
    except Exception as e:
        logger.error(f"获取回测列表失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取回测列表失败: {str(e)}")

@router.post("/", response_model=BacktestResponse, status_code=201)
async def create_backtest(
    backtest: BacktestCreate,
    background_tasks: BackgroundTasks
):
    """
    创建新回测
    """
    logger.info(f"创建新回测: {backtest.strategy_name}")
    try:
        # 记录详细请求信息
        logger.debug(f"回测请求详情: {backtest.dict()}")
        
        # 创建回测
        backtest_id = str(uuid.uuid4())
        new_backtest = await backtest_service.create_backtest(backtest_id, backtest)
        
        # 在后台运行回测
        background_tasks.add_task(
            backtest_service.run_backtest,
            backtest_id,
            backtest
        )
        
        logger.info(f"回测创建成功，ID: {backtest_id}")
        return new_backtest
    except Exception as e:
        logger.error(f"创建回测失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建回测失败: {str(e)}")

@router.get("/{backtest_id}")
async def get_backtest(backtest_id: str = Path(..., description="回测ID")):
    """
    获取单个回测详情
    """
    logger.info(f"获取回测详情: {backtest_id}")
    try:
        backtest = await backtest_service.get_backtest(backtest_id)
        if not backtest:
            logger.warning(f"回测不存在: {backtest_id}")
            raise HTTPException(status_code=404, detail="回测不存在")
        return backtest
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取回测详情失败: {str(e)}")

@router.delete("/{backtest_id}", status_code=204)
async def delete_backtest(backtest_id: str = Path(..., description="回测ID")):
    """
    删除回测
    """
    logger.info(f"删除回测: {backtest_id}")
    try:
        success = await backtest_service.delete_backtest(backtest_id)
        if not success:
            logger.warning(f"回测不存在: {backtest_id}")
            raise HTTPException(status_code=404, detail="回测不存在")
        return {"status": "success", "message": "回测已删除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除回测失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除回测失败: {str(e)}")

@router.post("/{backtest_id}/stop", status_code=200)
async def stop_backtest(backtest_id: str = Path(..., description="回测ID")):
    """
    停止正在运行的回测
    """
    logger.info(f"停止回测: {backtest_id}")
    try:
        success = await backtest_service.stop_backtest(backtest_id)
        if not success:
            logger.warning(f"停止回测失败: {backtest_id}")
            raise HTTPException(status_code=404, detail="回测不存在或已完成")
        return {"status": "success", "message": "回测已停止"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止回测失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"停止回测失败: {str(e)}")

@router.get("/{backtest_id}/results", status_code=200)
async def get_backtest_results(backtest_id: str = Path(..., description="回测ID")):
    """
    获取回测结果详情
    """
    logger.info(f"获取回测结果: {backtest_id}")
    try:
        results = await backtest_service.get_backtest_results(backtest_id)
        if not results:
            logger.warning(f"回测结果不存在: {backtest_id}")
            raise HTTPException(status_code=404, detail="回测结果不存在")
        return results
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测结果失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取回测结果失败: {str(e)}")

@router.get("/{backtest_id}/trades", status_code=200)
async def get_backtest_trades(backtest_id: str = Path(..., description="回测ID")):
    """
    获取回测交易记录
    """
    logger.info(f"获取回测交易记录: {backtest_id}")
    try:
        trades = await backtest_service.get_backtest_trades(backtest_id)
        if trades is None:
            logger.warning(f"回测交易记录不存在: {backtest_id}")
            raise HTTPException(status_code=404, detail="回测交易记录不存在")
        return trades
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测交易记录失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取回测交易记录失败: {str(e)}")

@router.get("/{backtest_id}/logs", status_code=200)
async def get_backtest_logs(
    backtest_id: str = Path(..., description="回测ID"),
    lines: int = Query(100, description="获取最新的N行日志", ge=1, le=1000)
):
    """
    获取回测任务执行日志
    """
    logger.info(f"获取回测日志: {backtest_id}, 行数: {lines}")
    try:
        logs = await backtest_service.get_backtest_logs(backtest_id, lines)
        return {
            "backtest_id": backtest_id,
            "logs": logs,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取回测日志失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取回测日志失败: {str(e)}")

@router.get("/{backtest_id}/download", status_code=200)
async def download_backtest_report(
    backtest_id: str = Path(..., description="回测ID"),
    format: str = Query("json", description="报告格式 (json, csv, html)")
):
    """
    下载回测报告
    """
    logger.info(f"下载回测报告: {backtest_id}, 格式: {format}")
    try:
        report = await backtest_service.generate_backtest_report(backtest_id, format)
        if not report:
            logger.warning(f"回测报告不存在: {backtest_id}")
            raise HTTPException(status_code=404, detail="回测报告不存在")
        return report
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载回测报告失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下载回测报告失败: {str(e)}")

@router.post("/cleanup-stuck")
async def cleanup_stuck_processes():
    """
    清理卡住的回测进程
    """
    logger.info("手动清理卡住的回测进程")
    try:
        cleaned_count = await backtest_service.cleanup_stuck_processes()
        return {
            "message": f"清理完成，共清理了 {cleaned_count} 个卡住的进程",
            "cleaned_count": cleaned_count
        }
    except Exception as e:
        logger.error(f"清理卡住进程失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"清理卡住进程失败: {str(e)}")

@router.post("/{backtest_id}/process-results")
async def process_backtest_results(backtest_id: str):
    """
    手动处理回测结果（用于修复卡住的回测）
    """
    logger.info(f"手动处理回测结果: {backtest_id}")
    try:
        success = await backtest_service.process_completed_backtest(backtest_id)
        if success:
            return {
                "message": f"回测结果处理成功: {backtest_id}",
                "backtest_id": backtest_id
            }
        else:
            raise HTTPException(status_code=404, detail="回测结果文件未找到或处理失败")
    except Exception as e:
        logger.error(f"处理回测结果失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"处理回测结果失败: {str(e)}")