"""
WebSocket路由模块
处理WebSocket连接和实时数据推送
"""

import asyncio
import json
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from app.services.websocket_service import websocket_service

logger = logging.getLogger(__name__)

# 创建WebSocket路由器
router = APIRouter()

@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    主WebSocket端点 - 实时数据推送
    
    消息格式:
    - 订阅: {"type": "subscribe", "topic": "dashboard|trading_status|system_status|notifications"}
    - 取消订阅: {"type": "unsubscribe", "topic": "dashboard"}
    - 心跳: {"type": "ping"}
    """
    await websocket_service.manager.connect(websocket)
    logger.info(f"📡 新的WebSocket连接建立")
    
    try:
        # 发送连接成功消息
        await websocket_service.manager.send_personal_message(
            json.dumps({
                "type": "connected", 
                "timestamp": asyncio.get_event_loop().time()
            }),
            websocket
        )
        
        # 消息处理循环
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await websocket_service.handle_message(websocket, message)
            except json.JSONDecodeError:
                await websocket_service.manager.send_personal_message(
                    json.dumps({"type": "error", "message": "Invalid JSON format"}),
                    websocket
                )
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await websocket_service.manager.send_personal_message(
                    json.dumps({
                        "type": "error", 
                        "message": f"Message processing failed: {str(e)}"
                    }),
                    websocket
                )
                
    except WebSocketDisconnect:
        websocket_service.manager.disconnect(websocket)
        logger.info("📡 WebSocket客户端正常断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
        websocket_service.manager.disconnect(websocket) 