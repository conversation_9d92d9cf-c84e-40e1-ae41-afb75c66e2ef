"""
仪表板API路由
提供实时交易数据和系统状态信息
"""

from fastapi import APIRouter, HTTPException, Query, Depends, WebSocket, WebSocketDisconnect
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
import json
from ..services.freqtrade_service import FreqtradeService
from ..services.websocket_service import ConnectionManager

logger = logging.getLogger(__name__)
router = APIRouter()

# 创建服务实例
freqtrade_service = FreqtradeService()
connection_manager = ConnectionManager()

@router.get("/overview")
async def get_dashboard_overview():
    """获取仪表板概览数据"""
    try:
        # 检查freqtrade连接状态
        is_connected = await freqtrade_service.ping()
        if not is_connected:
            return {
                "status": "offline",
                "message": "无法连接到Freqtrade API",
                "total_profit": 0,
                "total_profit_percentage": 0,
                "open_trades": 0,
                "closed_trades": 0,
                "win_rate": 0,
                "last_update": datetime.now().isoformat()
            }
        
        try:
            # 获取交易指标
            metrics = await freqtrade_service.calculate_trading_metrics()
            
            # 获取盈利数据
            profit_data = await freqtrade_service.get_profit()
            
            # 获取账户余额
            balance_data = await freqtrade_service.get_balance()
            
            return {
                "status": "running",  # 如果能连接就是running状态
                "total_profit": profit_data.get("profit_total_abs", 0),
                "total_profit_percentage": profit_data.get("profit_total_pct", 0),
                "open_trades": metrics.get("open_trades", 0),
                "closed_trades": metrics.get("closed_trades", 0),
                "win_rate": metrics.get("win_rate", 0),
                "balance": balance_data,
                "last_update": datetime.now().isoformat()
            }
        except Exception as inner_e:
            logger.error(f"获取详细数据失败，返回基本信息: {inner_e}")
            # 获取基本状态信息
            status_data = await freqtrade_service.get_status()
            version = await freqtrade_service.get_version()
            
            # 处理status可能是列表的情况（开放交易列表）
            open_trades_count = 0
            if isinstance(status_data, list):
                open_trades_count = len(status_data)
                strategy = status_data[0].get("strategy", "unknown") if status_data else "unknown"
            else:
                strategy = status_data.get("strategy", "unknown")
            
            return {
                "status": "running",
                "message": "部分数据获取失败",
                "total_profit": 0,
                "total_profit_percentage": 0,
                "open_trades": open_trades_count,
                "closed_trades": 0,
                "win_rate": 0,
                "strategy": strategy,
                "version": version.get("version", "unknown"),
                "last_update": datetime.now().isoformat()
            }
    except Exception as e:
        logger.error(f"获取仪表板概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@router.get("/trades/recent")
async def get_recent_trades(limit: int = Query(10, ge=1, le=100)):
    """获取最近的交易记录"""
    try:
        # 检查连接状态
        is_connected = await freqtrade_service.ping()
        if not is_connected:
            return {"trades": [], "total": 0}
        
        # 获取最近交易
        trades = await freqtrade_service.get_trades(limit=limit)
        
        # 格式化交易数据
        formatted_trades = []
        for trade in trades:
            formatted_trade = {
                "id": trade.get("trade_id", 0),
                "pair": trade.get("pair", ""),
                "side": "buy" if trade.get("is_short", False) else "sell",
                "amount": trade.get("amount", 0),
                "price": trade.get("open_rate", 0),
                "profit": trade.get("profit_abs", 0),
                "profit_percentage": trade.get("profit_pct", 0),
                "status": "open" if trade.get("is_open", True) else "closed",
                "open_time": trade.get("open_date", ""),
                "close_time": trade.get("close_date", ""),
                "strategy": trade.get("strategy", ""),
                "timestamp": trade.get("open_date", datetime.now().isoformat())
            }
            formatted_trades.append(formatted_trade)
        
        return {
            "trades": formatted_trades,
            "total": len(formatted_trades)
        }
    except Exception as e:
        logger.error(f"获取最近交易失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@router.get("/performance")
async def get_performance_metrics():
    """获取性能指标"""
    try:
        # 检查连接状态
        is_connected = await freqtrade_service.ping()
        if not is_connected:
            return {
                "daily_profit": [],
                "monthly_stats": {
                    "total_trades": 0,
                    "profitable_trades": 0,
                    "total_profit": 0
                },
                "performance_by_pair": []
            }
        
        # 获取每日盈利数据
        daily_profit_data = await freqtrade_service.get_daily_profit(days=30)
        
        # 获取性能数据
        performance_data = await freqtrade_service.get_performance()
        
        # 获取交易指标
        metrics = await freqtrade_service.calculate_trading_metrics()
        
        return {
            "daily_profit": daily_profit_data,
            "monthly_stats": {
                "total_trades": metrics.get("total_trades", 0),
                "profitable_trades": metrics.get("winning_trades", 0),
                "total_profit": metrics.get("total_profit", 0),
                "win_rate": metrics.get("win_rate", 0),
                "profit_factor": metrics.get("profit_factor", 0)
            },
            "performance_by_pair": performance_data
        }
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@router.get("/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查连接状态
        is_connected = await freqtrade_service.ping()
        
        if not is_connected:
            return {
                "connected": False,
                "freqtrade_status": "offline",
                "version": "unknown",
                "uptime": 0,
                "strategy": "unknown"
            }
        
        # 获取详细状态
        status_data = await freqtrade_service.get_status()
        version = await freqtrade_service.get_version()
        
        # 处理status可能是列表的情况（开放交易列表）
        if isinstance(status_data, list):
            # 如果是列表，说明是开放交易列表，表示机器人正在运行
            # 从第一个交易中获取策略名称
            strategy_name = status_data[0].get("strategy", "unknown") if status_data else "unknown"
            return {
                "connected": True,
                "freqtrade_status": "RUNNING",  # 有开放交易说明正在运行
                "version": version.get("version", "unknown"),
                "uptime": 0,  # 无法从交易列表获取uptime
                "strategy": strategy_name,
                "max_open_trades": len(status_data),
                "stake_currency": status_data[0].get("quote_currency", "USDT") if status_data else "USDT",
                "stake_amount": status_data[0].get("stake_amount", 0) if status_data else 0
            }
        else:
            # 原来的处理方式，status是字典
            return {
                "connected": True,
                "freqtrade_status": status_data.get("state", "unknown"),
                "version": version.get("version", "unknown"),
                "uptime": status_data.get("uptime", 0),
                "strategy": status_data.get("strategy", "unknown"),
                "max_open_trades": status_data.get("max_open_trades", 0),
                "stake_currency": status_data.get("stake_currency", "USDT"),
                "stake_amount": status_data.get("stake_amount", 0)
            }
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@router.get("/logs")
async def get_logs(limit: int = Query(100, ge=1, le=1000)):
    """获取系统日志"""
    try:
        # 检查连接状态
        is_connected = await freqtrade_service.ping()
        if not is_connected:
            return {"logs": [], "total": 0}
        
        # 获取日志
        logs = await freqtrade_service.get_logs(limit=limit)
        
        return {
            "logs": logs,
            "total": len(logs)
        }
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@router.get("/realtime")
async def get_realtime_data():
    """获取实时数据"""
    try:
        # 检查连接状态
        is_connected = await freqtrade_service.ping()
        if not is_connected:
            return {
                "timestamp": datetime.now().isoformat(),
                "bot_running": False,
                "open_trades_count": 0,
                "open_trades": [],
                "last_heartbeat": None
            }
        
        # 获取状态和交易数据
        status_data = await freqtrade_service.get_status()
        
        # 如果status是列表，说明是开放交易列表
        if isinstance(status_data, list):
            open_trades = status_data
            bot_running = True if open_trades else False
            last_heartbeat = datetime.now().isoformat()
        else:
            # 原来的处理方式，status是字典
            open_trades = await freqtrade_service.get_open_trades()
            bot_running = status_data.get("state") == "RUNNING"
            last_heartbeat = status_data.get("last_heartbeat")
        
        return {
            "timestamp": datetime.now().isoformat(),
            "bot_running": bot_running,
            "open_trades_count": len(open_trades),
            "open_trades": open_trades,
            "last_heartbeat": last_heartbeat
        }
    except Exception as e:
        logger.error(f"获取实时数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket实时数据推送端点"""
    await connection_manager.connect(websocket)
    
    # 发送初始数据
    overview_data = await get_dashboard_overview()
    await websocket.send_text(json.dumps({
        "type": "dashboard_update",
        "data": overview_data
    }))
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理消息（暂时只是回显）
            await websocket.send_text(json.dumps({
                "type": "message_received",
                "data": message
            }))
            
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
        logger.info("WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        connection_manager.disconnect(websocket) 