from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from . import Base

class Trade(Base):
    """交易记录模型"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    pair = Column(String(20), nullable=False)  # 交易对，如 BTC/USDT
    amount = Column(Float, nullable=False)  # 交易数量
    price = Column(Float, nullable=False)  # 交易价格
    side = Column(String(10), nullable=False)  # buy/sell
    status = Column(String(20), default="open")  # open/closed/canceled
    strategy = Column(String(50), nullable=False)  # 策略名称
    profit_loss = Column(Float, default=0.0)  # 盈亏
    profit_percentage = Column(Float, default=0.0)  # 盈亏百分比
    open_time = Column(DateTime, default=func.now())  # 开仓时间
    close_time = Column(DateTime, nullable=True)  # 平仓时间
    open_reason = Column(String(100), nullable=True)  # 开仓原因
    close_reason = Column(String(100), nullable=True)  # 平仓原因
    fee = Column(Float, default=0.0)  # 手续费
    exchange = Column(String(20), default="binance")  # 交易所
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class Strategy(Base):
    """策略配置模型"""
    __tablename__ = "strategies"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)  # 策略名称
    description = Column(Text, nullable=True)  # 策略描述
    class_name = Column(String(100), nullable=False)  # 策略类名
    file_path = Column(String(255), nullable=False)  # 策略文件路径
    parameters = Column(Text, nullable=True)  # 策略参数(JSON格式)
    is_active = Column(Boolean, default=False)  # 是否激活
    performance_score = Column(Float, default=0.0)  # 性能评分
    total_trades = Column(Integer, default=0)  # 总交易次数
    win_rate = Column(Float, default=0.0)  # 胜率
    avg_profit = Column(Float, default=0.0)  # 平均收益
    max_drawdown = Column(Float, default=0.0)  # 最大回撤
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class BacktestResult(Base):
    """回测结果模型"""
    __tablename__ = "backtest_results"
    
    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False)
    strategy = relationship("Strategy", backref="backtest_results")
    
    start_date = Column(DateTime, nullable=False)  # 回测开始日期
    end_date = Column(DateTime, nullable=False)  # 回测结束日期
    initial_balance = Column(Float, nullable=False)  # 初始资金
    final_balance = Column(Float, nullable=False)  # 最终资金
    total_return = Column(Float, nullable=False)  # 总收益率
    total_trades = Column(Integer, nullable=False)  # 总交易次数
    winning_trades = Column(Integer, nullable=False)  # 获利交易次数
    losing_trades = Column(Integer, nullable=False)  # 亏损交易次数
    win_rate = Column(Float, nullable=False)  # 胜率
    profit_factor = Column(Float, nullable=False)  # 盈利因子
    max_drawdown = Column(Float, nullable=False)  # 最大回撤
    max_drawdown_duration = Column(Integer, nullable=False)  # 最大回撤持续时间
    sharpe_ratio = Column(Float, nullable=True)  # 夏普比率
    sortino_ratio = Column(Float, nullable=True)  # 索提诺比率
    calmar_ratio = Column(Float, nullable=True)  # 卡玛比率
    avg_trade_duration = Column(Float, nullable=True)  # 平均交易持续时间
    parameters = Column(Text, nullable=True)  # 回测参数
    results_data = Column(Text, nullable=True)  # 详细结果数据(JSON)
    created_at = Column(DateTime, default=func.now())

class MarketData(Base):
    """市场数据模型"""
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False)  # 交易对符号
    timeframe = Column(String(10), nullable=False)  # 时间框架
    timestamp = Column(DateTime, nullable=False)  # 时间戳
    open = Column(Float, nullable=False)  # 开盘价
    high = Column(Float, nullable=False)  # 最高价
    low = Column(Float, nullable=False)  # 最低价
    close = Column(Float, nullable=False)  # 收盘价
    volume = Column(Float, nullable=False)  # 成交量
    created_at = Column(DateTime, default=func.now())

class SystemMetrics(Base):
    """系统监控指标模型"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(50), nullable=False)  # 指标名称
    metric_value = Column(Float, nullable=False)  # 指标值
    unit = Column(String(20), nullable=True)  # 单位
    category = Column(String(30), nullable=False)  # 分类：system/trading/performance
    timestamp = Column(DateTime, default=func.now())
    metadata = Column(Text, nullable=True)  # 额外元数据 