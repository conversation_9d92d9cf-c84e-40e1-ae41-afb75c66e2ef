"""
工具函数模块
"""

import httpx
import logging

logger = logging.getLogger(__name__)

async def check_freqtrade_status():
    """检查Freqtrade API状态"""
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get("http://127.0.0.1:8080/api/v1/status")
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"Freqtrade API返回状态码: {response.status_code}")
                return False
    except Exception as e:
        logger.warning(f"无法连接到Freqtrade API: {e}")
        return False 