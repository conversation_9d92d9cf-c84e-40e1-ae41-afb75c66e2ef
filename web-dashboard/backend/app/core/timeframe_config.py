"""
专业的时间周期配置模块
为虚拟币量化交易提供专业的时间周期定义和建议
"""

from typing import Dict, List, Any
from enum import Enum

class TimeframeCategory(Enum):
    """时间周期分类"""
    SCALPING = "scalping"      # 超短线 (1m-5m)
    SHORT_TERM = "short_term"  # 短线 (15m-1h)
    MEDIUM_TERM = "medium_term" # 中线 (2h-12h)
    LONG_TERM = "long_term"    # 长线 (1d+)

class TimeframeConfig:
    """专业的时间周期配置"""
    
    # 时间周期定义
    TIMEFRAMES = {
        # 超短线交易 - 高频率，高噪音，需要快速决策
        "1m": {
            "name": "1分钟",
            "category": TimeframeCategory.SCALPING,
            "minutes": 1,
            "description": "超短线交易，适合高频策略",
            "pros": ["信号频繁", "快速获利机会多"],
            "cons": ["噪音大", "手续费影响大", "需要快速决策"],
            "recommended_for": ["高频交易", "套利策略", "做市策略"],
            "min_capital": 1000,  # 最小建议资金(USDT)
            "risk_level": "极高"
        },
        "3m": {
            "name": "3分钟", 
            "category": TimeframeCategory.SCALPING,
            "minutes": 3,
            "description": "超短线交易，平衡频率和信号质量",
            "pros": ["信号较频繁", "噪音相对较少"],
            "cons": ["仍有较多噪音", "需要密切监控"],
            "recommended_for": ["短线突破策略", "动量策略"],
            "min_capital": 2000,
            "risk_level": "很高"
        },
        "5m": {
            "name": "5分钟",
            "category": TimeframeCategory.SCALPING, 
            "minutes": 5,
            "description": "经典短线周期，平衡频率和质量",
            "pros": ["经典短线周期", "信号相对可靠", "适合日内交易"],
            "cons": ["仍有噪音", "需要较多时间监控"],
            "recommended_for": ["日内交易", "趋势跟踪", "均线策略"],
            "min_capital": 3000,
            "risk_level": "高"
        },
        "15m": {
            "name": "15分钟",
            "category": TimeframeCategory.SHORT_TERM,
            "minutes": 15, 
            "description": "中短线交易，较好的信号质量",
            "pros": ["信号质量较好", "噪音减少", "适合技术分析"],
            "cons": ["信号频率降低", "错过短期机会"],
            "recommended_for": ["技术分析策略", "支撑阻力策略", "形态识别"],
            "min_capital": 5000,
            "risk_level": "中高"
        },
        "30m": {
            "name": "30分钟",
            "category": TimeframeCategory.SHORT_TERM,
            "minutes": 30,
            "description": "平衡的中短线周期",
            "pros": ["平衡频率和质量", "适合多种策略"],
            "cons": ["可能错过快速变化"],
            "recommended_for": ["均线交叉", "RSI策略", "布林带策略"],
            "min_capital": 5000,
            "risk_level": "中高"
        },
        "1h": {
            "name": "1小时",
            "category": TimeframeCategory.SHORT_TERM,
            "minutes": 60,
            "description": "经典中线周期，高质量信号",
            "pros": ["信号质量高", "噪音少", "适合大多数策略"],
            "cons": ["信号频率较低", "需要耐心等待"],
            "recommended_for": ["趋势跟踪", "均值回归", "多因子策略"],
            "min_capital": 10000,
            "risk_level": "中等"
        },
        "2h": {
            "name": "2小时",
            "category": TimeframeCategory.MEDIUM_TERM,
            "minutes": 120,
            "description": "中长线交易，减少噪音",
            "pros": ["噪音很少", "趋势明确", "适合趋势策略"],
            "cons": ["信号较少", "反应较慢"],
            "recommended_for": ["趋势策略", "突破策略", "长期持有"],
            "min_capital": 15000,
            "risk_level": "中等"
        },
        "4h": {
            "name": "4小时", 
            "category": TimeframeCategory.MEDIUM_TERM,
            "minutes": 240,
            "description": "长线交易，高质量信号",
            "pros": ["信号质量很高", "趋势清晰", "适合长期策略"],
            "cons": ["信号稀少", "资金利用率低"],
            "recommended_for": ["长期趋势", "价值投资", "定投策略"],
            "min_capital": 20000,
            "risk_level": "中低"
        },
        "6h": {
            "name": "6小时",
            "category": TimeframeCategory.MEDIUM_TERM, 
            "minutes": 360,
            "description": "日内趋势分析",
            "pros": ["日内趋势清晰", "噪音极少"],
            "cons": ["信号很少", "需要大资金"],
            "recommended_for": ["日内趋势", "波段交易"],
            "min_capital": 25000,
            "risk_level": "中低"
        },
        "12h": {
            "name": "12小时",
            "category": TimeframeCategory.MEDIUM_TERM,
            "minutes": 720,
            "description": "半日趋势分析",
            "pros": ["半日趋势明确", "适合大资金"],
            "cons": ["信号极少", "反应很慢"],
            "recommended_for": ["大资金管理", "长期配置"],
            "min_capital": 50000,
            "risk_level": "低"
        },
        "1d": {
            "name": "1天",
            "category": TimeframeCategory.LONG_TERM,
            "minutes": 1440,
            "description": "日线趋势，超长线交易",
            "pros": ["趋势最清晰", "适合长期投资"],
            "cons": ["信号极少", "资金占用时间长"],
            "recommended_for": ["长期投资", "资产配置", "定投"],
            "min_capital": 100000,
            "risk_level": "很低"
        },
        "3d": {
            "name": "3天",
            "category": TimeframeCategory.LONG_TERM,
            "minutes": 4320,
            "description": "多日趋势分析",
            "pros": ["长期趋势", "适合大资金配置"],
            "cons": ["信号罕见", "需要极大耐心"],
            "recommended_for": ["机构投资", "长期配置"],
            "min_capital": 500000,
            "risk_level": "很低"
        },
        "1w": {
            "name": "1周",
            "category": TimeframeCategory.LONG_TERM,
            "minutes": 10080,
            "description": "周线趋势，超长期投资",
            "pros": ["超长期趋势", "适合机构"],
            "cons": ["信号极罕见", "需要巨额资金"],
            "recommended_for": ["机构投资", "养老基金", "长期配置"],
            "min_capital": 1000000,
            "risk_level": "极低"
        }
    }
    
    @classmethod
    def get_timeframe_info(cls, timeframe: str) -> Dict[str, Any]:
        """获取时间周期信息"""
        return cls.TIMEFRAMES.get(timeframe, {})
    
    @classmethod
    def get_recommended_timeframes(cls, capital: float, risk_tolerance: str) -> List[str]:
        """根据资金和风险偏好推荐时间周期"""
        risk_map = {
            "conservative": ["1h", "2h", "4h", "1d"],      # 保守型
            "moderate": ["15m", "30m", "1h", "2h"],        # 稳健型  
            "aggressive": ["5m", "15m", "30m", "1h"],      # 激进型
            "speculative": ["1m", "3m", "5m", "15m"]       # 投机型
        }
        
        risk_timeframes = risk_map.get(risk_tolerance, ["1h", "4h"])
        
        # 根据资金筛选合适的时间周期
        suitable_timeframes = []
        for tf in risk_timeframes:
            tf_info = cls.TIMEFRAMES.get(tf, {})
            min_capital = tf_info.get("min_capital", 0)
            if capital >= min_capital:
                suitable_timeframes.append(tf)
        
        return suitable_timeframes or ["1h"]  # 默认返回1小时
    
    @classmethod
    def get_timeframes_by_category(cls, category: TimeframeCategory) -> List[str]:
        """按分类获取时间周期"""
        return [
            tf for tf, info in cls.TIMEFRAMES.items() 
            if info.get("category") == category
        ]
    
    @classmethod
    def validate_timeframe_combination(cls, timeframes: List[str]) -> Dict[str, Any]:
        """验证时间周期组合的合理性"""
        if not timeframes:
            return {"valid": False, "message": "至少需要选择一个时间周期"}
        
        categories = set()
        min_capital = 0
        
        for tf in timeframes:
            tf_info = cls.TIMEFRAMES.get(tf)
            if not tf_info:
                return {"valid": False, "message": f"不支持的时间周期: {tf}"}
            
            categories.add(tf_info["category"])
            min_capital = max(min_capital, tf_info.get("min_capital", 0))
        
        # 检查是否跨越太多分类（可能导致信号冲突）
        if len(categories) > 2:
            return {
                "valid": True, 
                "message": "警告: 选择的时间周期跨越多个分类，可能产生信号冲突",
                "min_capital": min_capital
            }
        
        return {
            "valid": True,
            "message": "时间周期组合合理",
            "min_capital": min_capital,
            "categories": list(categories)
        }
