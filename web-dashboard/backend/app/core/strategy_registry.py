"""
策略注册器
用于管理和注册所有可用的交易策略
"""

import logging
from typing import Dict, List, Optional, Type, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class StrategyInfo:
    """策略信息"""
    name: str
    description: str
    author: str
    version: str
    category: str
    timeframes: List[str]
    pairs: List[str]
    parameters: Dict[str, Any]

class StrategyRegistry:
    """策略注册器"""
    
    def __init__(self):
        self._strategies: Dict[str, StrategyInfo] = {}
        logger.info("策略注册器初始化完成")
    
    def register(self, strategy_info: StrategyInfo) -> None:
        """注册策略"""
        if strategy_info.name in self._strategies:
            logger.warning(f"策略 {strategy_info.name} 已存在，将被覆盖")
        
        self._strategies[strategy_info.name] = strategy_info
        logger.info(f"策略注册成功: {strategy_info.name} - {strategy_info.description}")
    
    def unregister(self, strategy_name: str) -> bool:
        """注销策略"""
        if strategy_name in self._strategies:
            del self._strategies[strategy_name]
            logger.info(f"策略注销成功: {strategy_name}")
            return True
        return False
    
    def get_strategy(self, strategy_name: str) -> Optional[StrategyInfo]:
        """获取策略信息"""
        return self._strategies.get(strategy_name)
    
    def get_all_strategies(self) -> Dict[str, StrategyInfo]:
        """获取所有策略"""
        return self._strategies.copy()
    
    def get_strategy_names(self) -> List[str]:
        """获取所有策略名称"""
        return list(self._strategies.keys())
    
    def get_strategies_by_category(self, category: str) -> Dict[str, StrategyInfo]:
        """根据类别获取策略"""
        return {
            name: info for name, info in self._strategies.items()
            if info.category == category
        }
    
    def is_registered(self, strategy_name: str) -> bool:
        """检查策略是否已注册"""
        return strategy_name in self._strategies
    
    def clear(self) -> None:
        """清空所有策略"""
        self._strategies.clear()
        logger.info("所有策略已清空")

# 全局策略注册器实例
strategy_registry = StrategyRegistry()

def register_strategy(
    name: str,
    description: str,
    author: str = "Unknown",
    version: str = "1.0.0",
    category: str = "technical",
    timeframes: List[str] = None,
    pairs: List[str] = None,
    parameters: Dict[str, Any] = None
) -> None:
    """
    策略注册装饰器函数
    
    Args:
        name: 策略名称
        description: 策略描述
        author: 作者
        version: 版本
        category: 类别 (technical, fundamental, arbitrage, etc.)
        timeframes: 支持的时间框架
        pairs: 支持的交易对
        parameters: 策略参数
    """
    if timeframes is None:
        # 专业的时间周期配置，按流动性和信号质量排序
        timeframes = [
            "1m",   # 超短线：高频交易，噪音多
            "3m",   # 短线：平衡频率和信号质量
            "5m",   # 短线：经典短线周期
            "15m",  # 中短线：较好的信号质量
            "30m",  # 中线：平衡周期
            "1h",   # 中线：经典中线周期
            "2h",   # 中长线：减少噪音
            "4h",   # 长线：高质量信号
            "6h",   # 长线：日内趋势
            "12h",  # 长线：半日趋势
            "1d",   # 超长线：日线趋势
            "3d",   # 超长线：多日趋势
            "1w"    # 超长线：周线趋势
        ]
    if pairs is None:
        pairs = ["BTC/USDT", "ETH/USDT"]
    if parameters is None:
        parameters = {}
    
    strategy_info = StrategyInfo(
        name=name,
        description=description,
        author=author,
        version=version,
        category=category,
        timeframes=timeframes,
        pairs=pairs,
        parameters=parameters
    )
    
    strategy_registry.register(strategy_info)
