import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import asyncio
import pymysql
import pymysql.cursors
import os

logger = logging.getLogger(__name__)

# 从环境变量或默认值获取数据库配置
DB_HOST = os.environ.get("DB_HOST", "localhost")
DB_PORT = int(os.environ.get("DB_PORT", "3306"))
DB_USER = os.environ.get("DB_USER", "root")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "123456")
DB_NAME = os.environ.get("DB_NAME", "freqtrade")
DB_CHARSET = os.environ.get("DB_CHARSET", "utf8mb4")

class BacktestDatabase:
    """使用MySQL的回测数据库实现"""
    
    @staticmethod
    def _get_connection():
        """获取数据库连接"""
        try:
            connection = pymysql.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME,
                cursorclass=pymysql.cursors.DictCursor,
                charset=DB_CHARSET
            )
            return connection
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            raise
    
    @staticmethod
    def _init_database():
        """初始化数据库表"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                # 创建回测表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS backtests (
                        id VARCHAR(36) PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        strategy_id VARCHAR(100) NOT NULL,
                        strategy_name VARCHAR(100) NOT NULL,
                        status VARCHAR(20) NOT NULL DEFAULT 'pending',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        config JSON,
                        results JSON,
                        error TEXT,
                        progress INT DEFAULT 0,
                        initial_balance DECIMAL(20, 8) DEFAULT 10000.0,
                        start_date VARCHAR(30),
                        end_date VARCHAR(30),
                        timeframe VARCHAR(10) DEFAULT '5m',
                        commission DECIMAL(10, 8) DEFAULT 0.1,
                        slippage DECIMAL(10, 8) DEFAULT 0.05,
                        max_open_trades INT DEFAULT 3,
                        stake_amount DECIMAL(20, 8) DEFAULT 100.0,
                        total_trades INT DEFAULT 0,
                        total_profit DECIMAL(20, 8) DEFAULT 0.0,
                        total_profit_pct DECIMAL(10, 4) DEFAULT 0.0,
                        max_drawdown DECIMAL(10, 4) DEFAULT 0.0,
                        sharpe_ratio DECIMAL(10, 4) DEFAULT 0.0,
                        final_balance DECIMAL(20, 8) DEFAULT 10000.0,
                        result_path VARCHAR(255)
                    )
                ''')
                
                # 创建交易记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trades (
                        id VARCHAR(36) PRIMARY KEY,
                        backtest_id VARCHAR(36) NOT NULL,
                        pair VARCHAR(20) NOT NULL,
                        side VARCHAR(10) NOT NULL,
                        amount DECIMAL(20, 8) NOT NULL,
                        entry_price DECIMAL(20, 8) NOT NULL,
                        exit_price DECIMAL(20, 8),
                        profit DECIMAL(20, 8) NOT NULL,
                        profit_pct DECIMAL(10, 4),
                        duration VARCHAR(30),
                        entry_time TIMESTAMP NOT NULL,
                        exit_time TIMESTAMP,
                        fees DECIMAL(10, 8) DEFAULT 0.0,
                        status VARCHAR(20) DEFAULT 'closed',
                        FOREIGN KEY (backtest_id) REFERENCES backtests (id)
                    )
                ''')
                
                # 创建资金曲线表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS equity_curve (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        backtest_id VARCHAR(36) NOT NULL,
                        date TIMESTAMP NOT NULL,
                        equity DECIMAL(20, 8) NOT NULL,
                        drawdown DECIMAL(10, 4) DEFAULT 0.0,
                        FOREIGN KEY (backtest_id) REFERENCES backtests (id)
                    )
                ''')
                
                conn.commit()
            conn.close()
            logger.info("✅ MySQL数据库表创建成功")
            
        except Exception as e:
            logger.error(f"❌ MySQL数据库初始化失败: {e}")
    
    @staticmethod
    async def create_backtest(backtest_data: Dict) -> bool:
        """创建回测记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                cursor.execute('''
                    INSERT INTO backtests (
                        id, name, strategy_id, strategy_name, status, created_at, updated_at,
                        config, results, error, progress, initial_balance, start_date, end_date,
                        timeframe, commission, slippage, max_open_trades, stake_amount
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    backtest_data["id"],
                    backtest_data["name"],
                    backtest_data["strategy_id"],
                    backtest_data["strategy_name"],
                    backtest_data["status"],
                    backtest_data["created_at"].isoformat(),
                    backtest_data["updated_at"].isoformat(),
                    json.dumps(backtest_data["config"]),
                    json.dumps(backtest_data["results"]) if backtest_data["results"] else None,
                    backtest_data["error"],
                    backtest_data["progress"],
                    backtest_data["initial_balance"],
                    backtest_data["start_date"],
                    backtest_data["end_date"],
                    backtest_data["timeframe"],
                    backtest_data["commission"],
                    backtest_data["slippage"],
                    backtest_data["max_open_trades"],
                    backtest_data["stake_amount"]
                ))
                
                conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建回测记录失败: {e}")
            return False
    
    @staticmethod
    async def update_backtest(backtest_id: str, update_data: Dict) -> bool:
        """更新回测记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                # 构建更新语句
                set_clauses = []
                values = []
                
                for key, value in update_data.items():
                    if key == "results":
                        set_clauses.append(f"{key} = %s")
                        values.append(json.dumps(value) if value else None)
                    elif key in ["created_at", "updated_at"] and isinstance(value, datetime):
                        set_clauses.append(f"{key} = %s")
                        values.append(value.isoformat())
                    else:
                        set_clauses.append(f"{key} = %s")
                        values.append(value)
                
                # 始终更新 updated_at
                if "updated_at" not in update_data:
                    set_clauses.append("updated_at = %s")
                    values.append(datetime.now().isoformat())
                
                values.append(backtest_id)
                
                query = f"UPDATE backtests SET {', '.join(set_clauses)} WHERE id = %s"
                cursor.execute(query, values)
                
                conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新回测记录失败: {e}")
            return False
    
    @staticmethod
    async def get_all_backtests() -> List[Dict]:
        """获取所有回测记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM backtests ORDER BY created_at DESC")
                rows = cursor.fetchall()
                
                backtests = []
                for row in rows:
                    # 安全解析JSON字段
                    try:
                        if row["config"]:
                            if isinstance(row["config"], str):
                                row["config"] = json.loads(row["config"])
                        if row["results"]:
                            if isinstance(row["results"], str):
                                row["results"] = json.loads(row["results"])
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败 for backtest {row['id']}: {e}")
                        # 保持原始数据，不中断整个查询
                        pass
                    
                    backtests.append(row)
                
            conn.close()
            return backtests
            
        except Exception as e:
            logger.error(f"❌ 获取回测列表失败: {e}")
            return []
    
    @staticmethod
    async def get_backtest_by_id(backtest_id: str) -> Optional[Dict]:
        """根据ID获取回测记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM backtests WHERE id = %s", (backtest_id,))
                row = cursor.fetchone()
                
                if row:
                    # 解析JSON字段
                    if row["config"]:
                        row["config"] = json.loads(row["config"])
                    if row["results"]:
                        row["results"] = json.loads(row["results"])
                    
                    conn.close()
                    return row
                
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取回测记录失败: {e}")
            return None
    
    @staticmethod
    async def delete_backtest(backtest_id: str) -> bool:
        """删除回测记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                # 首先删除关联的交易记录
                cursor.execute("DELETE FROM trades WHERE backtest_id = %s", (backtest_id,))
                
                # 删除关联的资金曲线数据
                cursor.execute("DELETE FROM equity_curve WHERE backtest_id = %s", (backtest_id,))
                
                # 最后删除回测记录
                cursor.execute("DELETE FROM backtests WHERE id = %s", (backtest_id,))
                
                conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除回测记录失败: {e}")
            return False
    
    @staticmethod
    async def save_trades(backtest_id: str, trades: List[Dict]) -> bool:
        """保存交易记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                for trade in trades:
                    cursor.execute('''
                        INSERT INTO trades (
                            id, backtest_id, pair, side, amount, entry_price, exit_price,
                            profit, profit_pct, duration, entry_time, exit_time, fees, status
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (
                        trade["id"],
                        backtest_id,
                        trade["pair"],
                        trade["side"],
                        trade["amount"],
                        trade["entry_price"],
                        trade.get("exit_price"),
                        trade["profit"],
                        trade.get("profit_pct"),
                        trade.get("duration"),
                        trade["entry_time"],
                        trade.get("exit_time"),
                        trade.get("fees", 0.0),
                        trade.get("status", "closed")
                    ))
                
                conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存交易记录失败: {e}")
            return False
    
    @staticmethod
    async def get_trades_by_backtest_id(backtest_id: str) -> List[Dict]:
        """获取回测的交易记录"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM trades WHERE backtest_id = %s ORDER BY entry_time", (backtest_id,))
                trades = cursor.fetchall()
                
            conn.close()
            return trades
            
        except Exception as e:
            logger.error(f"❌ 获取交易记录失败: {e}")
            return []
    
    @staticmethod
    async def save_equity_curve(backtest_id: str, equity_data: List[Dict]) -> bool:
        """保存资金曲线数据"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                for point in equity_data:
                    cursor.execute('''
                        INSERT INTO equity_curve (backtest_id, date, equity, drawdown)
                        VALUES (%s, %s, %s, %s)
                    ''', (
                        backtest_id,
                        point["date"],
                        point["equity"],
                        point.get("drawdown", 0.0)
                    ))
                
                conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存资金曲线失败: {e}")
            return False
    
    @staticmethod
    async def get_equity_curve_by_backtest_id(backtest_id: str) -> List[Dict]:
        """获取回测的资金曲线"""
        try:
            conn = BacktestDatabase._get_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM equity_curve WHERE backtest_id = %s ORDER BY date", (backtest_id,))
                curve = cursor.fetchall()
                
            conn.close()
            return curve
            
        except Exception as e:
            logger.error(f"❌ 获取资金曲线失败: {e}")
            return []

# 初始化数据库表
def create_tables():
    """创建数据库表"""
    BacktestDatabase._init_database()

# 数据库连接和断开连接函数
async def connect_database():
    """连接数据库"""
    create_tables()

async def disconnect_database():
    """断开数据库连接"""
    pass  # MySQL连接会在使用后自动关闭