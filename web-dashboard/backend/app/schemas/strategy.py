from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class StrategyStatus(str, Enum):
    """策略状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    DEPRECATED = "deprecated"

class StrategyType(str, Enum):
    """策略类型枚举"""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    ARBITRAGE = "arbitrage"
    SCALPING = "scalping"
    CUSTOM = "custom"

class StrategyBase(BaseModel):
    """策略基础模型"""
    name: str = Field(..., description="策略名称")
    description: Optional[str] = Field(None, description="策略描述")
    type: StrategyType = Field(..., description="策略类型")
    config: Dict[str, Any] = Field(default_factory=dict, description="策略配置")

class StrategyCreate(StrategyBase):
    """创建策略请求模型"""
    pass

class StrategyUpdate(BaseModel):
    """更新策略请求模型"""
    name: Optional[str] = Field(None, description="策略名称")
    description: Optional[str] = Field(None, description="策略描述")
    type: Optional[StrategyType] = Field(None, description="策略类型")
    config: Optional[Dict[str, Any]] = Field(None, description="策略配置")

class StrategyPerformance(BaseModel):
    """策略性能模型"""
    totalReturn: float = Field(default=0.0, description="总收益率")
    sharpeRatio: float = Field(default=0.0, description="夏普比率")
    maxDrawdown: float = Field(default=0.0, description="最大回撤")
    winRate: float = Field(default=0.0, description="胜率")

class StrategyResponse(BaseModel):
    """策略响应模型"""
    id: str = Field(..., description="策略ID")
    name: str = Field(..., description="策略名称")
    description: Optional[str] = Field(None, description="策略描述")
    status: str = Field(..., description="策略状态")
    type: str = Field(..., description="策略类型")
    performance: StrategyPerformance = Field(..., description="性能指标")
    config: Dict[str, Any] = Field(default_factory=dict, description="策略配置")
    createdAt: str = Field(..., description="创建时间")
    lastUpdated: str = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True

class StrategyBacktest(BaseModel):
    """策略回测模型"""
    strategy_id: str = Field(..., description="策略ID")
    timeframe: str = Field(..., description="时间框架")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    initial_balance: float = Field(default=10000.0, description="初始资金")
    pairs: List[str] = Field(default_factory=list, description="交易对列表")
    
class StrategyBacktestResult(BaseModel):
    """策略回测结果模型"""
    strategy_id: str = Field(..., description="策略ID")
    backtest_id: str = Field(..., description="回测ID")
    performance: StrategyPerformance = Field(..., description="性能指标")
    trades: List[Dict[str, Any]] = Field(default_factory=list, description="交易记录")
    equity_curve: List[Dict[str, Any]] = Field(default_factory=list, description="资金曲线")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="详细统计")
    created_at: datetime = Field(..., description="回测时间") 