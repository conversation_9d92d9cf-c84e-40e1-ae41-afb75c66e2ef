from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class TradeSide(str, Enum):
    """交易方向枚举"""
    BUY = "buy"
    SELL = "sell"

class TradeStatus(str, Enum):
    """交易状态枚举"""
    OPEN = "open"
    CLOSED = "closed"
    CANCELED = "canceled"

class TradeBase(BaseModel):
    """交易基础模型"""
    pair: str = Field(..., description="交易对")
    amount: float = Field(..., gt=0, description="交易数量")
    price: float = Field(..., gt=0, description="交易价格")
    side: TradeSide = Field(..., description="交易方向")
    strategy: str = Field(..., description="策略名称")
    exchange: str = Field(default="binance", description="交易所")

class TradeCreate(TradeBase):
    """创建交易请求模型"""
    open_reason: Optional[str] = Field(None, description="开仓原因")

class TradeUpdate(BaseModel):
    """更新交易请求模型"""
    status: Optional[TradeStatus] = None
    close_time: Optional[datetime] = None
    close_reason: Optional[str] = None
    profit_loss: Optional[float] = None
    profit_percentage: Optional[float] = None

class TradeResponse(TradeBase):
    """交易响应模型"""
    id: int
    status: TradeStatus
    profit_loss: float
    profit_percentage: float
    open_time: datetime
    close_time: Optional[datetime] = None
    open_reason: Optional[str] = None
    close_reason: Optional[str] = None
    fee: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TradeListResponse(BaseModel):
    """交易列表响应模型"""
    trades: List[TradeResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

class TradingMetrics(BaseModel):
    """交易指标模型"""
    total_trades: int = Field(..., description="总交易数")
    open_trades: int = Field(..., description="开仓交易数")
    closed_trades: int = Field(..., description="已平仓交易数")
    winning_trades: int = Field(..., description="盈利交易数")
    losing_trades: int = Field(..., description="亏损交易数")
    win_rate: float = Field(..., description="胜率")
    total_profit: float = Field(..., description="总收益")
    total_profit_percentage: float = Field(..., description="总收益率")
    avg_profit_per_trade: float = Field(..., description="平均每笔收益")
    max_profit: float = Field(..., description="最大单笔收益")
    max_loss: float = Field(..., description="最大单笔亏损")
    profit_factor: float = Field(..., description="盈利因子")

class OrderBookEntry(BaseModel):
    """订单簿条目"""
    price: float
    quantity: float

class OrderBook(BaseModel):
    """订单簿数据"""
    symbol: str
    bids: List[OrderBookEntry]
    asks: List[OrderBookEntry]
    timestamp: datetime

class TickerData(BaseModel):
    """行情数据"""
    symbol: str
    price: float
    change_24h: float
    change_percentage_24h: float
    volume_24h: float
    high_24h: float
    low_24h: float
    timestamp: datetime 