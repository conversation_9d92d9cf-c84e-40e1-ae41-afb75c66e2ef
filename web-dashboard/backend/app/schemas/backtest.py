from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class BacktestStatus(str, Enum):
    """回测状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class BacktestRequest(BaseModel):
    """回测请求模型"""
    strategy_name: str = Field(..., description="策略名称")
    timeframe: str = Field(..., description="时间框架", example="1h")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    initial_balance: float = Field(default=10000.0, description="初始资金")
    pairs: List[str] = Field(..., description="交易对列表")
    max_open_trades: int = Field(default=3, description="最大开仓数")
    stake_amount: float = Field(default=100.0, description="每次交易金额")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="策略参数")

class BacktestResponse(BaseModel):
    """回测响应模型"""
    backtest_id: str = Field(..., description="回测ID")
    status: BacktestStatus = Field(..., description="回测状态")
    strategy_name: str = Field(..., description="策略名称")
    timeframe: str = Field(..., description="时间框架")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    progress: float = Field(default=0.0, description="进度百分比")
    error_message: Optional[str] = Field(None, description="错误信息")

class BacktestResult(BaseModel):
    """回测结果模型"""
    backtest_id: str = Field(..., description="回测ID")
    strategy_name: str = Field(..., description="策略名称")
    
    # 基础统计
    total_trades: int = Field(default=0, description="总交易数")
    winning_trades: int = Field(default=0, description="盈利交易数")
    losing_trades: int = Field(default=0, description="亏损交易数")
    
    # 收益指标
    total_profit: float = Field(default=0.0, description="总收益")
    total_profit_percentage: float = Field(default=0.0, description="总收益率")
    final_balance: float = Field(default=0.0, description="最终余额")
    
    # 风险指标
    max_drawdown: float = Field(default=0.0, description="最大回撤")
    max_drawdown_percentage: float = Field(default=0.0, description="最大回撤百分比")
    calmar_ratio: Optional[float] = Field(None, description="卡玛比率")
    
    # 交易指标
    win_rate: float = Field(default=0.0, description="胜率")
    profit_factor: Optional[float] = Field(None, description="盈利因子")
    avg_profit_per_trade: float = Field(default=0.0, description="平均每笔盈利")
    avg_winning_trade: float = Field(default=0.0, description="平均盈利交易")
    avg_losing_trade: float = Field(default=0.0, description="平均亏损交易")
    
    # 时间指标
    avg_trade_duration: Optional[str] = Field(None, description="平均持仓时间")
    best_pair: Optional[str] = Field(None, description="最佳交易对")
    worst_pair: Optional[str] = Field(None, description="最差交易对")
    
    # 详细数据
    trades: List[Dict[str, Any]] = Field(default_factory=list, description="交易记录")
    daily_profit: List[Dict[str, Any]] = Field(default_factory=list, description="日收益")
    pair_performance: List[Dict[str, Any]] = Field(default_factory=list, description="交易对表现")
    
    created_at: datetime = Field(..., description="回测时间")

class BacktestComparison(BaseModel):
    """回测对比模型"""
    backtest_ids: List[str] = Field(..., description="回测ID列表")
    comparison_metrics: Dict[str, Any] = Field(default_factory=dict, description="对比指标")
    
class BacktestOptimization(BaseModel):
    """回测优化请求模型"""
    strategy_name: str = Field(..., description="策略名称")
    timeframe: str = Field(..., description="时间框架")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    parameter_ranges: Dict[str, Dict[str, Any]] = Field(..., description="参数优化范围")
    optimization_target: str = Field(default="total_profit", description="优化目标")
    max_iterations: int = Field(default=100, description="最大迭代次数")

class BacktestConfig(BaseModel):
    """回测配置"""
    timeframe: str = Field(..., description="时间框架")
    commission: float = Field(..., description="手续费率")
    slippage: float = Field(..., description="滑点")
    max_open_trades: int = Field(..., description="最大开仓数")
    stake_amount: float = Field(..., description="每次下单金额")

class BacktestCreate(BaseModel):
    """创建回测请求"""
    name: str = Field(..., description="回测名称")
    strategy_id: str = Field(..., description="策略ID")
    strategy_name: Optional[str] = Field(None, description="策略名称")
    start_date: datetime = Field(..., description="开始日期时间")
    end_date: datetime = Field(..., description="结束日期时间")
    initial_balance: float = Field(..., description="初始资金")
    timeframe: str = Field("1h", description="时间框架")
    commission: float = Field(0.1, description="手续费率(%)")
    slippage: float = Field(0.05, description="滑点(%)")
    max_open_trades: int = Field(3, description="最大开仓数")
    stake_amount: float = Field(100.0, description="每次下单金额")
    pairs: List[str] = Field(..., description="交易对列表")

class BacktestTrade(BaseModel):
    """回测交易记录"""
    id: str
    backtest_id: str
    pair: str
    side: str
    amount: float
    entry_price: float
    exit_price: float
    profit: float
    profit_pct: float
    duration: str
    entry_time: str
    exit_time: str
    fees: float
    status: str

class BacktestEquityPoint(BaseModel):
    """资金曲线数据点"""
    date: str
    equity: float
    drawdown: float

class BacktestSummary(BaseModel):
    """回测摘要"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    final_balance: float

class BacktestRiskMetrics(BaseModel):
    """风险指标"""
    volatility: float
    sortino_ratio: float
    calmar_ratio: float
    var_95: float
    cvar_95: float

class BacktestResults(BaseModel):
    """回测详细结果"""
    summary: BacktestSummary
    equity_curve: List[BacktestEquityPoint]
    monthly_returns: List[Dict[str, Any]]
    risk_metrics: BacktestRiskMetrics

class BacktestListResponse(BaseModel):
    """回测列表响应模型 - 匹配实际数据结构"""
    id: str = Field(..., description="回测ID")
    name: str = Field(..., description="回测名称")
    strategy_id: str = Field(..., description="策略ID")
    strategy_name: str = Field(..., description="策略名称")
    status: str = Field(..., description="回测状态")
    start_date: str = Field(..., description="开始日期")
    end_date: str = Field(..., description="结束日期")
    initial_balance: float = Field(..., description="初始资金")
    final_balance: float = Field(..., description="最终资金")
    total_return: float = Field(..., description="总收益率")
    sharpe_ratio: float = Field(..., description="夏普比率")
    max_drawdown: float = Field(..., description="最大回撤")
    win_rate: float = Field(..., description="胜率")
    total_trades: int = Field(..., description="总交易数")
    winning_trades: int = Field(..., description="盈利交易数")
    losing_trades: int = Field(..., description="亏损交易数")
    avg_win: float = Field(..., description="平均盈利")
    avg_loss: float = Field(..., description="平均亏损")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    config: Dict[str, Any] = Field(..., description="配置信息") 