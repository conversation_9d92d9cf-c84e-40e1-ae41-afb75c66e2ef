from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class AlertLevel(str, Enum):
    """警报级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertStatus(str, Enum):
    """警报状态枚举"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"

class SystemMetrics(BaseModel):
    """系统指标模型"""
    timestamp: datetime = Field(..., description="时间戳")
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    disk_usage: float = Field(..., description="磁盘使用率")
    network_in: float = Field(default=0.0, description="网络入流量")
    network_out: float = Field(default=0.0, description="网络出流量")
    active_connections: int = Field(default=0, description="活跃连接数")

class FreqtradeMetrics(BaseModel):
    """Freqtrade指标模型"""
    timestamp: datetime = Field(..., description="时间戳")
    status: str = Field(..., description="运行状态")
    uptime: int = Field(..., description="运行时间(秒)")
    open_trades: int = Field(..., description="开仓交易数")
    total_trades: int = Field(..., description="总交易数")
    current_profit: float = Field(..., description="当前收益")
    api_response_time: float = Field(..., description="API响应时间")
    last_heartbeat: datetime = Field(..., description="最后心跳时间")

class Alert(BaseModel):
    """警报模型"""
    id: str = Field(..., description="警报ID")
    level: AlertLevel = Field(..., description="警报级别")
    status: AlertStatus = Field(..., description="警报状态")
    title: str = Field(..., description="警报标题")
    message: str = Field(..., description="警报消息")
    source: str = Field(..., description="警报来源")
    created_at: datetime = Field(..., description="创建时间")
    acknowledged_at: Optional[datetime] = Field(None, description="确认时间")
    resolved_at: Optional[datetime] = Field(None, description="解决时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class AlertRule(BaseModel):
    """警报规则模型"""
    id: str = Field(..., description="规则ID")
    name: str = Field(..., description="规则名称")
    description: Optional[str] = Field(None, description="规则描述")
    metric: str = Field(..., description="监控指标")
    condition: str = Field(..., description="触发条件")
    threshold: float = Field(..., description="阈值")
    level: AlertLevel = Field(..., description="警报级别")
    enabled: bool = Field(default=True, description="是否启用")
    notification_channels: List[str] = Field(default_factory=list, description="通知渠道")

class PerformanceReport(BaseModel):
    """性能报告模型"""
    report_id: str = Field(..., description="报告ID")
    period_start: datetime = Field(..., description="统计开始时间")
    period_end: datetime = Field(..., description="统计结束时间")
    
    # 交易性能
    total_trades: int = Field(default=0, description="总交易数")
    profitable_trades: int = Field(default=0, description="盈利交易数")
    win_rate: float = Field(default=0.0, description="胜率")
    total_profit: float = Field(default=0.0, description="总收益")
    profit_percentage: float = Field(default=0.0, description="收益率")
    
    # 系统性能
    avg_cpu_usage: float = Field(default=0.0, description="平均CPU使用率")
    avg_memory_usage: float = Field(default=0.0, description="平均内存使用率")
    avg_api_response_time: float = Field(default=0.0, description="平均API响应时间")
    uptime_percentage: float = Field(default=0.0, description="正常运行时间百分比")
    
    # 警报统计
    total_alerts: int = Field(default=0, description="总警报数")
    critical_alerts: int = Field(default=0, description="严重警报数")
    warning_alerts: int = Field(default=0, description="警告数")
    
    created_at: datetime = Field(..., description="报告生成时间")

class HealthCheck(BaseModel):
    """健康检查模型"""
    service: str = Field(..., description="服务名称")
    status: str = Field(..., description="健康状态")
    timestamp: datetime = Field(..., description="检查时间")
    response_time: float = Field(..., description="响应时间")
    details: Dict[str, Any] = Field(default_factory=dict, description="详细信息")

class MonitoringDashboard(BaseModel):
    """监控仪表板模型"""
    system_metrics: SystemMetrics = Field(..., description="系统指标")
    freqtrade_metrics: FreqtradeMetrics = Field(..., description="Freqtrade指标")
    recent_alerts: List[Alert] = Field(default_factory=list, description="最近警报")
    health_checks: List[HealthCheck] = Field(default_factory=list, description="健康检查")
    last_updated: datetime = Field(..., description="最后更新时间") 