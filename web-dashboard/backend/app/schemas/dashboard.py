from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class DashboardOverview(BaseModel):
    """仪表板概览模型"""
    status: str = Field(..., description="系统状态")
    total_profit: float = Field(default=0.0, description="总收益")
    total_profit_percentage: float = Field(default=0.0, description="总收益率")
    open_trades: int = Field(default=0, description="开仓交易数")
    closed_trades: int = Field(default=0, description="已关闭交易数")
    win_rate: float = Field(default=0.0, description="胜率")
    balance: Dict[str, Any] = Field(default_factory=dict, description="账户余额")
    last_update: datetime = Field(..., description="最后更新时间")

class TradingMetrics(BaseModel):
    """交易指标模型"""
    total_trades: int = Field(default=0, description="总交易数")
    open_trades: int = Field(default=0, description="开仓交易数")
    closed_trades: int = Field(default=0, description="已关闭交易数")
    winning_trades: int = Field(default=0, description="盈利交易数")
    losing_trades: int = Field(default=0, description="亏损交易数")
    win_rate: float = Field(default=0.0, description="胜率")
    
    # 收益指标
    total_profit: float = Field(default=0.0, description="总收益")
    total_profit_percentage: float = Field(default=0.0, description="总收益率")
    realized_profit: float = Field(default=0.0, description="已实现收益")
    unrealized_profit: float = Field(default=0.0, description="未实现收益")
    
    # 风险指标
    max_drawdown: float = Field(default=0.0, description="最大回撤")
    current_drawdown: float = Field(default=0.0, description="当前回撤")
    
    # 时间指标
    avg_trade_duration: Optional[str] = Field(None, description="平均持仓时间")
    
    last_update: datetime = Field(..., description="最后更新时间")

class OrderBook(BaseModel):
    """订单簿模型"""
    symbol: str = Field(..., description="交易对")
    bids: List[List[float]] = Field(..., description="买单")
    asks: List[List[float]] = Field(..., description="卖单")
    timestamp: datetime = Field(..., description="时间戳")

class TickerData(BaseModel):
    """行情数据模型"""
    symbol: str = Field(..., description="交易对")
    price: float = Field(..., description="当前价格")
    change_24h: float = Field(..., description="24小时变化")
    change_percentage_24h: float = Field(..., description="24小时变化百分比")
    volume_24h: float = Field(..., description="24小时成交量")
    high_24h: float = Field(..., description="24小时最高价")
    low_24h: float = Field(..., description="24小时最低价")
    timestamp: datetime = Field(..., description="时间戳")

class PairPerformance(BaseModel):
    """交易对表现模型"""
    pair: str = Field(..., description="交易对")
    trade_count: int = Field(default=0, description="交易次数")
    profit: float = Field(default=0.0, description="总收益")
    profit_percentage: float = Field(default=0.0, description="收益率")
    win_rate: float = Field(default=0.0, description="胜率")
    avg_trade_duration: Optional[str] = Field(None, description="平均持仓时间")

class StrategyPerformanceSummary(BaseModel):
    """策略表现摘要模型"""
    strategy_name: str = Field(..., description="策略名称")
    status: str = Field(..., description="策略状态")
    total_trades: int = Field(default=0, description="总交易数")
    open_trades: int = Field(default=0, description="开仓交易数")
    profit: float = Field(default=0.0, description="收益")
    profit_percentage: float = Field(default=0.0, description="收益率")
    win_rate: float = Field(default=0.0, description="胜率")
    last_trade_time: Optional[datetime] = Field(None, description="最后交易时间")

class RealtimeData(BaseModel):
    """实时数据模型"""
    timestamp: datetime = Field(..., description="时间戳")
    data_type: str = Field(..., description="数据类型")
    content: Dict[str, Any] = Field(..., description="数据内容")

class DashboardStats(BaseModel):
    """仪表板统计模型"""
    overview: DashboardOverview = Field(..., description="概览数据")
    trading_metrics: TradingMetrics = Field(..., description="交易指标")
    pair_performance: List[PairPerformance] = Field(default_factory=list, description="交易对表现")
    strategy_performance: List[StrategyPerformanceSummary] = Field(default_factory=list, description="策略表现")
    recent_trades: List[Dict[str, Any]] = Field(default_factory=list, description="最近交易")
    market_data: List[TickerData] = Field(default_factory=list, description="市场数据")
    last_updated: datetime = Field(..., description="最后更新时间") 