import logging
import os
import psutil
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import uuid
import asyncio
import aiohttp
import random

logger = logging.getLogger(__name__)

class MonitoringService:
    """系统监控服务"""
    
    def __init__(self):
        self.alerts: Dict[str, dict] = {}
        self.logs: List[dict] = []
        # 从环境变量或配置中获取代理设置
        self.proxy_url = os.getenv("HTTPS_PROXY") or os.getenv("HTTP_PROXY") or "http://127.0.0.1:7897"
        self.proxies = {
            "http": self.proxy_url,
            "https": self.proxy_url
        }

        # 初始化一些示例数据
        self._init_sample_data()
    
    def _init_sample_data(self):
        """初始化示例监控数据"""
        # 生成示例日志
        log_levels = ["info", "warning", "error"]
        log_messages = [
            "系统启动成功",
            "Freqtrade连接建立",
            "策略执行完成",
            "数据同步完成",
            "内存使用率较高",
            "API响应超时",
            "数据库连接异常",
            "交易执行成功",
            "策略信号生成",
            "风险检查通过"
        ]
        
        for i in range(50):
            level = random.choice(log_levels)
            message = random.choice(log_messages)
            timestamp = datetime.now() - timedelta(minutes=random.randint(1, 1440))
            
            self.logs.append({
                "id": str(uuid.uuid4()),
                "timestamp": timestamp.isoformat(),
                "level": level,
                "message": message,
                "source": random.choice(["system", "freqtrade", "database", "api"])
            })
        
        # 按时间倒序排列
        self.logs.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # 生成示例告警
        if random.random() < 0.3:  # 30%概率有告警
            alert_id = str(uuid.uuid4())
            self.alerts[alert_id] = {
                "id": alert_id,
                "type": "high_cpu",
                "level": "warning",
                "title": "CPU使用率过高",
                "message": "系统CPU使用率超过85%，请检查运行程序",
                "created_at": (datetime.now() - timedelta(minutes=30)).isoformat(),
                "acknowledged": False,
                "acknowledged_at": None,
                "resolved": False
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态概览"""
        try:
            # 获取系统运行时间
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            uptime_hours = int(uptime.total_seconds() // 3600)
            
            # 检查Freqtrade状态
            freqtrade_status = await self._check_freqtrade_status()
            
            return {
                "freqtrade": {
                    "status": freqtrade_status.get("status", "unknown"),
                    "uptime": freqtrade_status.get("uptime", "unknown"),
                    "version": freqtrade_status.get("version", "unknown")
                },
                "api": {
                    "status": "running",
                    "uptime": f"{uptime_hours}h",
                    "version": "1.0.0"
                },
                "database": {
                    "status": "connected",
                    "uptime": f"{uptime_hours}h",
                    "type": "SQLite"
                },
                "system": {
                    "status": "running",
                    "uptime": f"{uptime_hours}h",
                    "os": "Linux" if psutil.LINUX else ("Windows" if psutil.WINDOWS else "macOS")
                }
            }
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                "freqtrade": {"status": "unknown"},
                "api": {"status": "running"},
                "database": {"status": "unknown"},
                "system": {"status": "unknown"}
            }
    
    async def _check_freqtrade_status(self) -> Dict[str, Any]:
        """检查Freqtrade状态"""
        try:
            connector = aiohttp.TCPConnector(limit=100, ssl=False, proxy=self.proxy_url)
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    "http://localhost:8080/api/v1/status",
                    auth=aiohttp.BasicAuth("admin", "password"),
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "running",
                            "uptime": "running",
                            "version": data.get("version", "unknown")
                        }
                    else:
                        return {"status": "error"}
        except Exception as e:
            logger.error(f"检查Freqtrade状态失败: {e}")
            return {"status": "disconnected"}
    
    async def get_resource_usage(self) -> Dict[str, Any]:
        """获取资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = memory.used / (1024**3)  # GB
            memory_total = memory.total / (1024**3)  # GB
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_used = disk.used / (1024**3)  # GB
            disk_total = disk.total / (1024**3)  # GB
            
            return {
                "cpu": {
                    "usage": round(cpu_percent, 1),
                    "cores": psutil.cpu_count()
                },
                "memory": {
                    "usage": round(memory_percent, 1),
                    "used": round(memory_used, 1),
                    "total": round(memory_total, 1),
                    "unit": "GB"
                },
                "disk": {
                    "usage": round(disk_percent, 1),
                    "used": round(disk_used, 1),
                    "total": round(disk_total, 1),
                    "unit": "GB"
                }
            }
        except Exception as e:
            logger.error(f"获取资源使用情况失败: {e}")
            return {
                "cpu": {"usage": 0, "cores": 1},
                "memory": {"usage": 0, "used": 0, "total": 1, "unit": "GB"},
                "disk": {"usage": 0, "used": 0, "total": 1, "unit": "GB"}
            }
    
    async def get_performance_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能指标趋势"""
        try:
            # 生成模拟的时间序列数据
            now = datetime.now()
            time_points = []
            cpu_data = []
            memory_data = []
            trade_counts = []
            
            # 生成过去N小时的数据点
            for i in range(hours):
                timestamp = now - timedelta(hours=hours-i)
                time_points.append(timestamp.strftime("%H:%M"))
                
                # 模拟CPU使用率（20-80%之间波动）
                base_cpu = 45 + random.uniform(-15, 15)
                cpu_data.append(round(max(20, min(80, base_cpu)), 1))
                
                # 模拟内存使用率（30-70%之间波动）
                base_memory = 50 + random.uniform(-10, 10)
                memory_data.append(round(max(30, min(70, base_memory)), 1))
                
                # 模拟每小时交易次数（0-15次）
                trades = random.randint(0, 15) if i < hours//2 else random.randint(0, 8)
                trade_counts.append(trades)
            
            return {
                "timeRange": f"{hours}h",
                "timestamps": time_points,
                "metrics": {
                    "cpu_usage": {
                        "name": "CPU使用率",
                        "unit": "%",
                        "data": cpu_data,
                        "current": cpu_data[-1] if cpu_data else 0,
                        "average": round(sum(cpu_data) / len(cpu_data), 1) if cpu_data else 0
                    },
                    "memory_usage": {
                        "name": "内存使用率",
                        "unit": "%",
                        "data": memory_data,
                        "current": memory_data[-1] if memory_data else 0,
                        "average": round(sum(memory_data) / len(memory_data), 1) if memory_data else 0
                    },
                    "trade_frequency": {
                        "name": "每小时交易次数",
                        "unit": "次",
                        "data": trade_counts,
                        "current": trade_counts[-1] if trade_counts else 0,
                        "total": sum(trade_counts) if trade_counts else 0
                    }
                }
            }
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {
                "timeRange": f"{hours}h",
                "timestamps": [],
                "metrics": {}
            }
    
    async def get_system_logs(self, level: str = "all", limit: int = 100) -> Dict[str, Any]:
        """获取系统日志"""
        try:
            filtered_logs = self.logs
            
            if level != "all":
                filtered_logs = [log for log in self.logs if log["level"] == level]
            
            # 限制返回数量
            limited_logs = filtered_logs[:limit]
            
            return {
                "logs": limited_logs,
                "total": len(filtered_logs),
                "filtered": len(limited_logs),
                "level": level,
                "stats": {
                    "info": len([log for log in self.logs if log["level"] == "info"]),
                    "warning": len([log for log in self.logs if log["level"] == "warning"]),
                    "error": len([log for log in self.logs if log["level"] == "error"])
                }
            }
        except Exception as e:
            logger.error(f"获取系统日志失败: {e}")
            return {
                "logs": [],
                "total": 0,
                "filtered": 0,
                "level": level,
                "stats": {"info": 0, "warning": 0, "error": 0}
            }
    
    async def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        try:
            active_alerts = [
                alert for alert in self.alerts.values() 
                if not alert["resolved"]
            ]
            
            # 按创建时间倒序排列
            return sorted(active_alerts, key=lambda x: x["created_at"], reverse=True)
        except Exception as e:
            logger.error(f"获取活跃告警失败: {e}")
            return []
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """确认告警"""
        try:
            if alert_id not in self.alerts:
                return False
            
            self.alerts[alert_id]["acknowledged"] = True
            self.alerts[alert_id]["acknowledged_at"] = datetime.now().isoformat()
            
            return True
        except Exception as e:
            logger.error(f"确认告警失败: {e}")
            return False
    
    async def get_health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查各个组件的健康状态
            checks = {
                "api": await self._check_api_health(),
                "freqtrade": await self._check_freqtrade_health(),
                "database": await self._check_database_health(),
                "system": await self._check_system_health()
            }
            
            # 计算总体健康状态
            all_healthy = all(check["healthy"] for check in checks.values())
            
            return {
                "healthy": all_healthy,
                "timestamp": datetime.now().isoformat(),
                "checks": checks
            }
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "healthy": False,
                "timestamp": datetime.now().isoformat(),
                "checks": {},
                "error": str(e)
            }
    
    async def _check_api_health(self) -> Dict[str, Any]:
        """检查API健康状态"""
        return {
            "healthy": True,
            "response_time": "< 100ms",
            "message": "API运行正常"
        }
    
    async def _check_freqtrade_health(self) -> Dict[str, Any]:
        """检查Freqtrade健康状态"""
        try:
            status = await self._check_freqtrade_status()
            healthy = status.get("status") == "running"
            
            return {
                "healthy": healthy,
                "response_time": "< 200ms" if healthy else "timeout",
                "message": "Freqtrade运行正常" if healthy else "Freqtrade连接失败"
            }
        except Exception:
            return {
                "healthy": False,
                "response_time": "timeout",
                "message": "Freqtrade连接失败"
            }
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        return {
            "healthy": True,
            "response_time": "< 50ms",
            "message": "数据库连接正常"
        }
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        try:
            resources = await self.get_resource_usage()
            cpu_ok = resources["cpu"]["usage"] < 90
            memory_ok = resources["memory"]["usage"] < 90
            disk_ok = resources["disk"]["usage"] < 90
            
            healthy = cpu_ok and memory_ok and disk_ok
            
            return {
                "healthy": healthy,
                "response_time": "< 10ms",
                "message": "系统资源正常" if healthy else "系统资源使用率过高"
            }
        except Exception:
            return {
                "healthy": False,
                "response_time": "error",
                "message": "系统检查失败"
            }
    
    async def get_freqtrade_status(self) -> Dict[str, Any]:
        """获取Freqtrade详细状态"""
        return await self._check_freqtrade_status()
    
    async def get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        try:
            return {
                "status": "connected",
                "type": "SQLite",
                "size": "125.6 MB",
                "tables": 8,
                "last_backup": "2024-12-28 10:00:00",
                "connections": 3
            }
        except Exception as e:
            logger.error(f"获取数据库状态失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def get_trading_stats(self) -> Dict[str, Any]:
        """获取交易统计"""
        try:
            # 模拟交易统计数据
            now = datetime.now()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            
            return {
                "today": {
                    "trades": random.randint(5, 25),
                    "profit": round(random.uniform(-50, 150), 2),
                    "volume": round(random.uniform(5000, 25000), 2)
                },
                "this_week": {
                    "trades": random.randint(25, 150),
                    "profit": round(random.uniform(-200, 800), 2),
                    "volume": round(random.uniform(25000, 150000), 2)
                },
                "this_month": {
                    "trades": random.randint(100, 600),
                    "profit": round(random.uniform(-500, 3000), 2),
                    "volume": round(random.uniform(100000, 600000), 2)
                },
                "active_trades": random.randint(2, 8),
                "open_orders": random.randint(0, 5),
                "last_trade": (now - timedelta(minutes=random.randint(5, 120))).isoformat()
            }
        except Exception as e:
            logger.error(f"获取交易统计失败: {e}")
            return {
                "today": {"trades": 0, "profit": 0, "volume": 0},
                "this_week": {"trades": 0, "profit": 0, "volume": 0},
                "this_month": {"trades": 0, "profit": 0, "volume": 0},
                "active_trades": 0,
                "open_orders": 0,
                "last_trade": None
            }

# 创建服务实例
monitoring_service = MonitoringService() 