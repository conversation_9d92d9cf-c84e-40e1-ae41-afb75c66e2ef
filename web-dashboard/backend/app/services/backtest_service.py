import logging
import os
import json
import uuid
import asyncio
import subprocess
import signal
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import aiohttp

from app.database import BacktestDatabase

logger = logging.getLogger(__name__)

class BacktestService:
    """回测服务 - 真正的单例模式 + 数据库持久化"""
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BacktestService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        logger.info("初始化回测服务...")
        self.enable_real_backtest = True  # 是否启用真实回测
        self.freqtrade_path = self._find_freqtrade_path()
        self.config_template = self._get_config_template()
        self.running_backtests = {}  # 存储正在运行的回测进程
        self._initialized = True
        
        # 创建回测结果目录
        self.backtest_results_dir = Path("backtest_results")
        self.backtest_results_dir.mkdir(exist_ok=True)
        
        logger.info(f"回测服务初始化完成，结果将保存到: {self.backtest_results_dir.absolute()}")
        logger.info(f"Freqtrade路径: {self.freqtrade_path}")
        
    def _find_freqtrade_path(self) -> str:
        """查找freqtrade可执行文件路径"""
        # 获取当前文件的目录
        current_dir = Path(__file__).parent

        # 尝试多个可能的虚拟环境路径
        possible_venv_paths = [
            current_dir / "../../../venv_freqtrade/bin/freqtrade",
            Path("venv_freqtrade/bin/freqtrade"),
            Path("../venv_freqtrade/bin/freqtrade"),
            Path("../../venv_freqtrade/bin/freqtrade"),
        ]

        for venv_path in possible_venv_paths:
            if venv_path.exists():
                logger.info(f"在虚拟环境中找到freqtrade: {venv_path.absolute()}")
                return str(venv_path.absolute())

        # 尝试在系统路径中查找
        try:
            result = subprocess.run(["which", "freqtrade"], capture_output=True, text=True)
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"在系统路径中找到freqtrade: {path}")
                return path
        except Exception as e:
            logger.warning(f"查找freqtrade时出错: {e}")

        logger.warning("未找到freqtrade可执行文件，将使用'freqtrade'作为命令")
        return "freqtrade"

    async def _verify_proxy_connection(self) -> bool:
        """验证代理连接是否正常"""
        try:
            # 从环境变量读取代理配置
            proxy_host = os.getenv("PROXY_HOST", "127.0.0.1")
            proxy_port = os.getenv("PROXY_PORT", "7897")
            proxy_url = f"http://{proxy_host}:{proxy_port}"
            test_url = "https://api.binance.com/api/v3/ping"

            logger.info(f"验证代理连接: {proxy_url}")

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    test_url,
                    proxy=proxy_url,
                    timeout=aiohttp.ClientTimeout(total=10),
                    ssl=False
                ) as response:
                    if response.status == 200:
                        logger.info("代理连接验证成功")
                        return True
                    else:
                        logger.warning(f"代理连接验证失败，状态码: {response.status}")
                        return False
        except Exception as e:
            logger.warning(f"代理连接验证异常: {e}")
            return False

    def _get_config_template(self) -> Dict:
        """获取回测配置模板"""
        try:
            # 获取当前文件的目录
            current_dir = Path(__file__).parent

            # 尝试多个可能的配置文件路径 - 优先使用带代理的配置
            possible_paths = [
                # 从当前服务文件位置向上查找 - 优先带代理
                current_dir / "../../../config/config_backtest_with_proxy.json",
                current_dir / "../../../config/config_backtest.json",
                # 从项目根目录查找 - 优先带代理
                Path("config/config_backtest_with_proxy.json"),
                Path("config/config_backtest.json"),
                # 相对路径 - 优先带代理
                Path("../config/config_backtest_with_proxy.json"),
                Path("../config/config_backtest.json"),
                Path("../../config/config_backtest_with_proxy.json"),
                Path("../../config/config_backtest.json"),
            ]

            config_path = None
            for path in possible_paths:
                if path.exists():
                    config_path = path
                    break

            if not config_path:
                logger.warning(f"配置模板不存在，尝试的路径: {[str(p.absolute()) for p in possible_paths]}")
                return {}
            
            with open(config_path, "r") as f:
                config = json.load(f)
                logger.info(f"加载配置模板成功: {config_path.absolute()}")
                return config
        except Exception as e:
            logger.error(f"加载配置模板失败: {e}")
            return {}
    
    async def create_backtest(self, backtest_id: str, backtest_data) -> Dict:
        """创建回测记录"""
        logger.info(f"创建回测记录: {backtest_id}")
        
        # 转换为字典格式
        if hasattr(backtest_data, 'dict'):
            backtest_dict = backtest_data.dict()
        else:
            backtest_dict = backtest_data
            
        # 处理config中的datetime对象
        config_dict = backtest_dict.copy()
        if "start_date" in config_dict and hasattr(config_dict["start_date"], 'isoformat'):
            config_dict["start_date"] = config_dict["start_date"].isoformat()
        if "end_date" in config_dict and hasattr(config_dict["end_date"], 'isoformat'):
            config_dict["end_date"] = config_dict["end_date"].isoformat()
        
        # 创建回测记录
        backtest_record = {
            "id": backtest_id,
            "name": backtest_dict["name"],
            "strategy_id": backtest_dict["strategy_id"],
            "strategy_name": backtest_dict["strategy_name"],
            "status": "pending",
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "config": config_dict,
            "results": None,
            "error": None,
            "progress": 0,
            "initial_balance": backtest_dict["initial_balance"],
            "start_date": backtest_dict["start_date"].isoformat() if hasattr(backtest_dict["start_date"], 'isoformat') else str(backtest_dict["start_date"]),
            "end_date": backtest_dict["end_date"].isoformat() if hasattr(backtest_dict["end_date"], 'isoformat') else str(backtest_dict["end_date"]),
            "timeframe": backtest_dict["timeframe"],
            "commission": backtest_dict.get("commission", 0.1),
            "slippage": backtest_dict.get("slippage", 0.05),
            "max_open_trades": backtest_dict["max_open_trades"],
            "stake_amount": backtest_dict["stake_amount"]
        }
        
        # 保存到数据库
        success = await BacktestDatabase.create_backtest(backtest_record)
        if not success:
            raise Exception("保存回测记录到数据库失败")
            
        # 返回响应格式
        return {
            "backtest_id": backtest_id,
            "status": "pending",
            "strategy_name": backtest_dict["strategy_name"],
            "timeframe": backtest_dict["timeframe"],
            "start_date": backtest_record["start_date"],
            "end_date": backtest_record["end_date"],
            "created_at": backtest_record["created_at"],
            "completed_at": None,
            "progress": 0.0,
            "error_message": None
        }

    async def download_data_if_needed(self, pairs: List[str], timeframe: str, start_date: str, end_date: str) -> tuple[bool, bool]:
        """自动下载缺失的市场数据"""
        try:
            logger.info(f"检查并下载数据: {pairs}, {timeframe}, {start_date} - {end_date}")
            
            # 获取配置文件路径 - 优先使用带代理的配置
            current_dir = Path(__file__).parent
            possible_paths = [
                # 优先选择带代理的配置
                current_dir / "../../../../config/config_backtest_with_proxy.json",
                current_dir / "../../../../config/config_backtest.json",
                current_dir / "../../../../config/config_backtest_no_proxy.json",
                current_dir / "../../../config/config_backtest_with_proxy.json",
                current_dir / "../../../config/config_backtest.json",
                current_dir / "../../../config/config_backtest_no_proxy.json",
                current_dir / "../../config/config_backtest_with_proxy.json",
                current_dir / "../../config/config_backtest.json",
                current_dir / "../../config/config_backtest_no_proxy.json",
                Path("config/config_backtest_with_proxy.json"),
                Path("config/config_backtest.json"),
                Path("config/config_backtest_no_proxy.json"),
            ]

            config_path = None
            for path in possible_paths:
                logger.info(f"检查配置文件路径: {path.absolute()}, 存在: {path.exists()}")
                if path.exists():
                    config_path = path
                    logger.info(f"选择配置文件: {config_path.absolute()}")
                    break

            if not config_path:
                logger.error(f"找不到配置文件，尝试的路径: {[str(p.absolute()) for p in possible_paths]}")
                return False, True  # 失败时默认使用代理

            # 根据配置文件名判断是否使用代理
            use_proxy = "no_proxy" not in config_path.name
            logger.info(f"根据配置文件 {config_path.name} 确定使用代理: {use_proxy}")
            
            # 确保数据目录存在 - freqtrade默认保存在binance目录下
            # 使用与freqtrade命令相同的路径
            # 注意：freqtrade命令使用的是backend目录下的user_data
            backend_dir = Path(__file__).parent.parent  # 从services目录到backend目录
            user_data_dir = backend_dir / "user_data"
            data_dir = user_data_dir / "data" / "binance"
            data_dir.mkdir(parents=True, exist_ok=True)
            
            # 解析日期格式 - 处理不同类型的输入
            if isinstance(start_date, str):
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            else:
                start_dt = start_date
                
            if isinstance(end_date, str):
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            else:
                end_dt = end_date
            
            # 提前3天以确保有足够的历史数据用于指标计算
            download_start_dt = start_dt - timedelta(days=3)
            
            # 格式化为freqtrade需要的格式
            start_str = download_start_dt.strftime('%Y%m%d')
            end_str = end_dt.strftime('%Y%m%d')
            
            # 为每个交易对下载数据
            for pair in pairs:
                # 使用精确到时分秒的文件命名格式
                pair_name = pair.replace('/', '_')
                start_time_str = start_dt.strftime("%Y%m%d_%H%M%S")
                end_time_str = end_dt.strftime("%Y%m%d_%H%M%S")
                filename = f"{pair_name}-{timeframe}-{start_time_str}-{end_time_str}.json"
                file_path = data_dir / filename
                
                # 检查是否已有覆盖请求时间范围的数据文件
                existing_file = self._find_existing_data_file(data_dir, pair_name, timeframe, start_dt, end_dt)
                if existing_file:
                    logger.info(f"找到已有数据文件覆盖请求范围: {existing_file}")
                    # 复用已有数据文件
                    success = await self._reuse_existing_data(
                        existing_file, data_dir, pair_name, timeframe, filename, file_path, start_dt, end_dt
                    )

                    if not success:
                        logger.error(f"复用已有数据失败: {pair} {timeframe}")
                        return False, use_proxy
                else:
                    # 需要下载新数据
                    logger.info(f"下载新数据: {filename}")

                    # 如果文件存在，先删除旧文件
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"删除旧数据文件: {filename}")

                    # 执行数据下载命令
                    cmd = [
                        self.freqtrade_path,
                        "download-data",
                        "--config", str(config_path.absolute()),
                        "--exchange", "binance",
                        "--pairs", pair,
                        "--timeframes", timeframe,
                        "--timerange", f"{start_str}-{end_str}",
                        "--data-format-ohlcv", "json",
                        "--user-data-dir", str(user_data_dir)
                    ]

                    # 注意：--skip-exchange-checks 和 --skip-pair-validation 参数在新版本freqtrade中已被移除
                    # 如果需要跳过检查，可以在配置文件中设置相应选项

                    logger.info(f"执行命令: {' '.join(cmd)}")

                    # 准备环境变量，确保代理设置被传递给子进程
                    env = os.environ.copy()
                    proxy_host = os.getenv("PROXY_HOST", "127.0.0.1")
                    proxy_port = os.getenv("PROXY_PORT", "7897")
                    proxy_url = f"http://{proxy_host}:{proxy_port}"
                    env["HTTP_PROXY"] = proxy_url
                    env["HTTPS_PROXY"] = proxy_url
                    env["http_proxy"] = proxy_url
                    env["https_proxy"] = proxy_url
                    env["ALL_PROXY"] = proxy_url
                    env["all_proxy"] = proxy_url
                    env["REQUESTS_CA_BUNDLE"] = ""  # 禁用SSL验证
                    env["CURL_CA_BUNDLE"] = ""      # 禁用SSL验证
                    logger.info(f"为数据下载子进程设置代理环境变量: {proxy_url}")

                    # 启动数据下载进程，设置超时时间
                    logger.info(f"启动数据下载进程: {pair} {timeframe}")
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        cwd=Path(__file__).parent.parent,
                        env=env  # 传递包含代理设置的环境变量
                    )

                    try:
                        # 设置超时时间为5分钟
                        stdout, stderr = await asyncio.wait_for(
                            process.communicate(),
                            timeout=300.0
                        )
                        logger.info(f"数据下载进程完成: {pair} {timeframe}, 返回码: {process.returncode}")

                    except asyncio.TimeoutError:
                        logger.error(f"数据下载超时: {pair} {timeframe}")
                        process.kill()
                        await process.wait()
                        return False, use_proxy
                    except Exception as e:
                        logger.error(f"数据下载进程异常: {pair} {timeframe}, 错误: {e}")
                        if process.returncode is None:
                            process.kill()
                            await process.wait()
                        return False, use_proxy

                    if process.returncode == 0:
                        logger.info(f"数据下载成功: {pair} {timeframe}")
                        logger.info(f"下载输出: {stdout.decode()}")

                        # 验证和处理下载的数据文件
                        success = await self._process_downloaded_data(
                            data_dir, pair_name, timeframe, filename, file_path, start_dt, end_dt
                        )

                        if not success:
                            logger.error(f"数据文件处理失败: {pair} {timeframe}")
                            return False, use_proxy

                    else:
                        logger.error(f"数据下载失败: {pair} {timeframe}")
                        logger.error(f"错误输出: {stderr.decode()}")
                        return False, use_proxy
            
            return True, use_proxy
            
        except Exception as e:
            logger.error(f"数据下载异常: {e}")
            return False, True  # 异常时默认使用代理

    async def _process_downloaded_data(self, data_dir: Path, pair_name: str, timeframe: str,
                                     filename: str, file_path: Path, start_dt: datetime, end_dt: datetime) -> bool:
        """
        处理下载的数据文件：验证、重命名、复制到spot目录

        Args:
            data_dir: 数据目录
            pair_name: 交易对名称
            timeframe: 时间周期
            filename: 目标文件名（带时间范围）
            file_path: 目标文件路径
            start_dt: 开始时间
            end_dt: 结束时间

        Returns:
            bool: 处理是否成功
        """
        try:
            # 1. 检查freqtrade下载的标准文件名
            standard_filename = f"{pair_name}-{timeframe}.json"
            standard_file_path = data_dir / standard_filename

            logger.info(f"检查标准文件: {standard_file_path}")
            logger.info(f"标准文件存在: {standard_file_path.exists()}")

            if not standard_file_path.exists():
                logger.error(f"标准数据文件不存在: {standard_filename}")
                return False

            # 2. 复制标准文件为带时间范围的文件名
            import shutil
            shutil.copy2(standard_file_path, file_path)
            logger.info(f"创建时间范围文件: {filename}")

            # 3. 验证文件内容
            if not file_path.exists():
                logger.error(f"时间范围文件创建失败: {filename}")
                return False

            # 4. 数据质量验证
            validation_result = await self._validate_market_data(file_path, pair_name.replace('_', '/'), timeframe, start_dt, end_dt)
            if not validation_result["valid"]:
                logger.error(f"数据质量验证失败: {validation_result['error']}")
                return False

            # 5. 验证数据时间范围是否覆盖请求范围
            time_range_valid = await self._validate_data_time_range(file_path, start_dt, end_dt)
            if not time_range_valid:
                logger.error(f"数据时间范围不覆盖请求范围: 请求 {start_dt} - {end_dt}")
                return False

            logger.info(f"数据质量验证通过: {validation_result['summary']}")

            # 5. 复制到spot目录
            spot_dir = data_dir / "spot"
            spot_dir.mkdir(parents=True, exist_ok=True)

            # 复制时间范围文件到spot目录
            spot_file_path = spot_dir / filename
            shutil.copy2(file_path, spot_file_path)
            logger.info(f"时间范围文件已复制到spot目录: {spot_file_path}")

            # 复制标准格式文件到spot目录
            standard_spot_file_path = spot_dir / standard_filename
            if standard_spot_file_path.exists():
                standard_spot_file_path.unlink()
            shutil.copy2(file_path, standard_spot_file_path)
            logger.info(f"标准格式文件已复制到spot目录: {standard_spot_file_path}")

            return True

        except Exception as e:
            logger.error(f"处理下载数据时出错: {e}")
            return False

    async def _reuse_existing_data(self, existing_file: Path, data_dir: Path, pair_name: str,
                                 timeframe: str, filename: str, file_path: Path,
                                 start_dt: datetime, end_dt: datetime) -> bool:
        """
        复用已有的数据文件

        Args:
            existing_file: 已有的数据文件路径
            data_dir: 数据目录
            pair_name: 交易对名称
            timeframe: 时间周期
            filename: 目标文件名
            file_path: 目标文件路径
            start_dt: 开始时间
            end_dt: 结束时间

        Returns:
            bool: 复用是否成功
        """
        try:
            import shutil

            # 1. 复制已有文件到当前文件名（如果不是同一个文件）
            if existing_file != file_path:
                shutil.copy2(existing_file, file_path)
                logger.info(f"复用已有数据文件: {filename}")
            else:
                logger.info(f"已有数据文件就是目标文件: {filename}")

            # 2. 验证复用的数据质量
            validation_result = await self._validate_market_data(file_path, pair_name.replace('_', '/'), timeframe, start_dt, end_dt)
            if not validation_result["valid"]:
                logger.error(f"复用数据质量验证失败: {validation_result['error']}")
                return False

            logger.info(f"复用数据质量验证通过: {validation_result['summary']}")

            # 3. 复制到spot目录
            spot_dir = data_dir / "spot"
            spot_dir.mkdir(parents=True, exist_ok=True)

            # 复制时间范围文件到spot目录
            spot_file_path = spot_dir / filename
            shutil.copy2(file_path, spot_file_path)
            logger.info(f"复用文件已复制到spot目录: {spot_file_path}")

            # 创建标准格式文件到spot目录
            standard_filename = f"{pair_name}-{timeframe}.json"
            standard_spot_file_path = spot_dir / standard_filename
            if standard_spot_file_path.exists():
                standard_spot_file_path.unlink()
            shutil.copy2(file_path, standard_spot_file_path)
            logger.info(f"创建标准格式文件: {standard_spot_file_path}")

            return True

        except Exception as e:
            logger.error(f"复用已有数据时出错: {e}")
            return False

    async def _validate_data_time_range(self, file_path: Path, start_dt: datetime, end_dt: datetime) -> bool:
        """
        验证数据文件的时间范围是否覆盖请求的时间范围

        Args:
            file_path: 数据文件路径
            start_dt: 请求的开始时间
            end_dt: 请求的结束时间

        Returns:
            bool: 时间范围是否有效
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            if not data or len(data) == 0:
                logger.error("数据文件为空")
                return False

            # 获取数据的时间范围
            first_timestamp = data[0][0] / 1000  # 转换为秒
            last_timestamp = data[-1][0] / 1000
            data_start_dt = datetime.fromtimestamp(first_timestamp)
            data_end_dt = datetime.fromtimestamp(last_timestamp)

            logger.info(f"数据时间范围: {data_start_dt} - {data_end_dt}")
            logger.info(f"请求时间范围: {start_dt} - {end_dt}")

            # 检查数据是否覆盖请求的时间范围
            # 允许一定的时间缓冲（1小时）
            buffer = timedelta(hours=1)

            if data_start_dt > (start_dt + buffer):
                logger.error(f"数据开始时间太晚: 数据开始于 {data_start_dt}, 请求开始于 {start_dt}")
                return False

            if data_end_dt < (end_dt - buffer):
                logger.error(f"数据结束时间太早: 数据结束于 {data_end_dt}, 请求结束于 {end_dt}")
                return False

            logger.info("数据时间范围验证通过")
            return True

        except Exception as e:
            logger.error(f"验证数据时间范围时出错: {e}")
            return False

    async def cleanup_stuck_processes(self) -> Dict[str, Any]:
        """
        清理卡住的freqtrade进程

        Returns:
            Dict: 清理结果统计
        """
        try:
            import subprocess
            import signal

            # 查找所有freqtrade进程
            result = subprocess.run(
                ["ps", "aux"],
                capture_output=True,
                text=True
            )

            freqtrade_processes = []
            for line in result.stdout.split('\n'):
                if 'freqtrade' in line and ('backtesting' in line or 'download-data' in line):
                    parts = line.split()
                    if len(parts) > 1:
                        try:
                            pid = int(parts[1])
                            freqtrade_processes.append({
                                'pid': pid,
                                'command': ' '.join(parts[10:])
                            })
                        except (ValueError, IndexError):
                            continue

            logger.info(f"找到 {len(freqtrade_processes)} 个freqtrade进程")

            # 清理进程
            killed_count = 0
            for proc in freqtrade_processes:
                try:
                    pid = proc['pid']
                    logger.info(f"终止进程 PID {pid}: {proc['command'][:100]}...")

                    # 先尝试优雅终止
                    subprocess.run(['kill', '-TERM', str(pid)], check=False)

                    # 等待2秒
                    await asyncio.sleep(2)

                    # 检查进程是否还存在
                    try:
                        subprocess.run(['kill', '-0', str(pid)], check=True)
                        # 进程还存在，强制终止
                        subprocess.run(['kill', '-KILL', str(pid)], check=False)
                        logger.info(f"强制终止进程 PID {pid}")
                    except subprocess.CalledProcessError:
                        # 进程已经不存在
                        logger.info(f"进程 PID {pid} 已优雅终止")

                    killed_count += 1

                except Exception as e:
                    logger.error(f"终止进程 {proc['pid']} 时出错: {e}")

            # 更新卡住的回测任务状态
            updated_count = await self._update_stuck_backtests()

            return {
                "success": True,
                "processes_found": len(freqtrade_processes),
                "processes_killed": killed_count,
                "backtests_updated": updated_count,
                "message": f"清理了 {killed_count} 个进程，更新了 {updated_count} 个回测任务状态"
            }

        except Exception as e:
            logger.error(f"清理卡住进程时出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "清理进程失败"
            }

    async def _update_stuck_backtests(self) -> int:
        """
        更新卡住的回测任务状态为失败

        Returns:
            int: 更新的任务数量
        """
        try:
            from datetime import datetime, timedelta

            # 获取所有运行中的回测任务
            all_backtests = await BacktestDatabase.get_all_backtests()
            running_backtests = [bt for bt in all_backtests if bt.get('status') == 'running']

            updated_count = 0
            current_time = datetime.now()

            for backtest in running_backtests:
                try:
                    # 检查回测任务是否运行时间过长（超过10分钟）
                    created_at = backtest.get('created_at')
                    if isinstance(created_at, str):
                        created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

                    if created_at and (current_time - created_at).total_seconds() > 600:  # 10分钟
                        backtest_id = backtest.get('id')
                        logger.info(f"更新超时回测任务状态: {backtest_id}")

                        await BacktestDatabase.update_backtest(backtest_id, {
                            "status": "failed",
                            "error": "回测任务超时，已被系统清理",
                            "progress": 100,
                            "completed_at": current_time.isoformat()
                        })

                        updated_count += 1

                except Exception as e:
                    logger.error(f"更新回测任务状态时出错: {e}")
                    continue

            return updated_count

        except Exception as e:
            logger.error(f"更新卡住回测任务状态时出错: {e}")
            return 0

    def _find_existing_data_file(self, data_dir: Path, pair_name: str, timeframe: str,
                                start_dt: datetime, end_dt: datetime) -> Path:
        """
        查找已有的数据文件，检查是否覆盖请求的时间范围

        Args:
            data_dir: 数据目录
            pair_name: 交易对名称 (如 BTC_USDT)
            timeframe: 时间周期 (如 5m)
            start_dt: 请求开始时间
            end_dt: 请求结束时间

        Returns:
            如果找到覆盖范围的文件则返回文件路径，否则返回None
        """
        try:
            # 搜索匹配的数据文件
            pattern = f"{pair_name}-{timeframe}-*.json"
            matching_files = list(data_dir.glob(pattern))

            for file_path in matching_files:
                # 解析文件名中的时间范围
                filename = file_path.name
                # 格式: BTC_USDT-5m-20250720_000000-20250725_235959.json
                parts = filename.replace('.json', '').split('-')
                if len(parts) >= 4:
                    try:
                        file_start_str = parts[2]  # 20250720_000000
                        file_end_str = parts[3]    # 20250725_235959

                        # 解析时间
                        file_start_dt = datetime.strptime(file_start_str, "%Y%m%d_%H%M%S")
                        file_end_dt = datetime.strptime(file_end_str, "%Y%m%d_%H%M%S")

                        # 检查是否覆盖请求的时间范围
                        if file_start_dt <= start_dt and file_end_dt >= end_dt:
                            logger.info(f"找到覆盖范围的数据文件: {filename}")
                            logger.info(f"文件范围: {file_start_dt} - {file_end_dt}")
                            logger.info(f"请求范围: {start_dt} - {end_dt}")
                            return file_path

                    except ValueError as e:
                        logger.warning(f"解析文件时间范围失败: {filename}, 错误: {e}")
                        continue

            return None

        except Exception as e:
            logger.error(f"查找已有数据文件时出错: {e}")
            return None

    async def _validate_market_data(self, file_path: Path, pair: str, timeframe: str, start_dt: datetime, end_dt: datetime) -> Dict[str, Any]:
        """
        专业的市场数据质量验证

        验证内容：
        1. 数据完整性：记录数量是否符合预期
        2. 时间连续性：是否有缺失的时间点
        3. 价格合理性：OHLC价格关系是否正确
        4. 成交量合理性：成交量是否为正数
        5. 数据范围：是否覆盖请求的时间范围
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            if not data or len(data) == 0:
                return {"valid": False, "error": "数据文件为空"}

            # 计算时间周期的分钟数
            timeframe_minutes = self._get_timeframe_minutes(timeframe)
            if timeframe_minutes is None:
                return {"valid": False, "error": f"不支持的时间周期: {timeframe}"}

            # 计算预期的数据点数量
            time_diff = end_dt - start_dt
            expected_points = int(time_diff.total_seconds() / (timeframe_minutes * 60))
            actual_points = len(data)

            # 数据完整性检查（允许10%的误差，因为市场可能有休市时间）
            completeness_ratio = actual_points / expected_points if expected_points > 0 else 0
            if completeness_ratio < 0.7:  # 少于70%认为数据不完整
                return {
                    "valid": False,
                    "error": f"数据不完整: 预期{expected_points}条，实际{actual_points}条 (完整度: {completeness_ratio:.1%})"
                }

            # 价格数据质量检查
            invalid_candles = 0
            zero_volume_count = 0

            for i, candle in enumerate(data):
                if len(candle) < 6:  # [timestamp, open, high, low, close, volume]
                    invalid_candles += 1
                    continue

                timestamp, open_price, high, low, close, volume = candle[:6]

                # 检查OHLC价格关系
                if not (low <= open_price <= high and low <= close <= high):
                    invalid_candles += 1
                    continue

                # 检查价格是否为正数
                if any(price <= 0 for price in [open_price, high, low, close]):
                    invalid_candles += 1
                    continue

                # 统计零成交量
                if volume == 0:
                    zero_volume_count += 1

            # 计算数据质量指标
            invalid_ratio = invalid_candles / actual_points if actual_points > 0 else 0
            zero_volume_ratio = zero_volume_count / actual_points if actual_points > 0 else 0

            # 数据质量阈值检查
            if invalid_ratio > 0.05:  # 超过5%的无效K线
                return {
                    "valid": False,
                    "error": f"数据质量差: {invalid_ratio:.1%}的K线数据无效"
                }

            # 时间范围检查
            if data:
                first_timestamp = data[0][0] / 1000  # 转换为秒
                last_timestamp = data[-1][0] / 1000
                first_time = datetime.fromtimestamp(first_timestamp)
                last_time = datetime.fromtimestamp(last_timestamp)

                # 检查是否覆盖请求的时间范围（允许一定误差）
                start_buffer = start_dt - timedelta(days=1)
                end_buffer = end_dt + timedelta(days=1)

                if first_time > start_buffer or last_time < end_buffer:
                    logger.warning(f"数据时间范围可能不完整: 数据范围 {first_time} - {last_time}, 请求范围 {start_dt} - {end_dt}")

            # 生成验证摘要
            summary = (
                f"{actual_points}条K线数据, "
                f"完整度: {completeness_ratio:.1%}, "
                f"无效率: {invalid_ratio:.1%}, "
                f"零成交量: {zero_volume_ratio:.1%}"
            )

            return {
                "valid": True,
                "summary": summary,
                "metrics": {
                    "total_candles": actual_points,
                    "expected_candles": expected_points,
                    "completeness_ratio": completeness_ratio,
                    "invalid_candles": invalid_candles,
                    "invalid_ratio": invalid_ratio,
                    "zero_volume_count": zero_volume_count,
                    "zero_volume_ratio": zero_volume_ratio
                }
            }

        except Exception as e:
            return {"valid": False, "error": f"数据验证异常: {str(e)}"}

    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """获取时间周期对应的分钟数"""
        timeframe_map = {
            "1m": 1, "3m": 3, "5m": 5, "15m": 15, "30m": 30,
            "1h": 60, "2h": 120, "4h": 240, "6h": 360, "12h": 720,
            "1d": 1440, "3d": 4320, "1w": 10080
        }
        return timeframe_map.get(timeframe)
    
    async def run_backtest(self, backtest_id: str, backtest_data) -> None:
        """运行回测（后台任务）"""
        try:
            logger.info(f"开始运行回测: {backtest_id}")
            
            # 更新状态为运行中
            await BacktestDatabase.update_backtest(backtest_id, {"status": "running", "progress": 0})
            
            # 转换为字典格式
            if hasattr(backtest_data, 'dict'):
                backtest_dict = backtest_data.dict()
            else:
                backtest_dict = backtest_data
            
            # 调用真实的回测逻辑（不等待完成，只启动）
            result = await self.start_backtest(backtest_dict, backtest_id)
            
            # 注意：不在这里更新为completed！
            # 状态更新由_monitor_backtest_process处理
            # 这里只记录启动成功
            logger.info(f"回测任务已启动，ID: {backtest_id}, 等待进程完成...")
            
        except Exception as e:
            logger.error(f"回测运行失败: {backtest_id}, 错误: {e}")
            await BacktestDatabase.update_backtest(backtest_id, {
                "status": "failed", 
                "error": str(e)
            })
    
    async def get_backtests(self, skip: int = 0, limit: int = 100) -> List[Dict]:
        """获取回测列表"""
        logger.info(f"获取回测列表: skip={skip}, limit={limit}")
        backtests = await BacktestDatabase.get_all_backtests()
        
        # 应用分页
        return backtests[skip:skip + limit]
    
    async def get_backtest(self, backtest_id: str) -> Optional[Dict]:
        """获取单个回测详情"""
        return await BacktestDatabase.get_backtest_by_id(backtest_id)

    async def start_backtest(self, backtest_params: Dict, backtest_id: str = None) -> Dict:
        """启动回测（集成数据下载）"""
        try:
            logger.info(f"开始启动回测，参数: {backtest_params}")

            # 验证代理连接
            proxy_ok = await self._verify_proxy_connection()
            if not proxy_ok:
                logger.warning("代理连接验证失败，但继续尝试回测...")

            # 使用传入的ID或生成新的回测ID
            if backtest_id is None:
                backtest_id = str(uuid.uuid4())

            # 提取参数用于数据下载
            pairs = backtest_params.get("pairs", ["BTC/USDT"])
            timeframe = backtest_params.get("timeframe", "5m")
            start_date = backtest_params.get("start_date", "")
            end_date = backtest_params.get("end_date", "")

            # 自动下载缺失的数据
            logger.info("开始检查并下载所需的数据...")
            download_success, use_proxy = await self.download_data_if_needed(pairs, timeframe, start_date, end_date)

            if not download_success:
                error_msg = "数据下载失败，回测无法继续"
                logger.error(error_msg)
                await BacktestDatabase.update_backtest(backtest_id, {
                    "status": "failed",
                    "error": error_msg,
                    "progress": 100
                })
                return {
                    "error": error_msg,
                    "status": "failed",
                    "message": error_msg
                }
            
            # 创建回测配置
            config = self._create_backtest_config(backtest_params, use_proxy)
            
            # 保存配置到文件
            config_path = self.backtest_results_dir / f"config_{backtest_id}.json"
            with open(config_path, "w") as f:
                json.dump(config, f, indent=4)
            logger.info(f"已保存回测配置到: {config_path}")
            
            # 构建回测命令
            cmd = self._build_backtest_command(backtest_id, config_path, backtest_params)
            logger.info(f"回测命令: {' '.join(cmd)}")
            
            # 准备环境变量，确保代理设置被传递给子进程
            env = os.environ.copy()
            proxy_host = os.getenv("PROXY_HOST", "127.0.0.1")
            proxy_port = os.getenv("PROXY_PORT", "7897")
            proxy_url = f"http://{proxy_host}:{proxy_port}"
            env["HTTP_PROXY"] = proxy_url
            env["HTTPS_PROXY"] = proxy_url
            env["REQUESTS_CA_BUNDLE"] = ""  # 禁用SSL验证
            env["CURL_CA_BUNDLE"] = ""      # 禁用SSL验证
            logger.info(f"为回测子进程设置代理环境变量: {proxy_url}")

            # 启动回测进程，传递环境变量
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env  # 传递包含代理设置的环境变量
            )
            
            # 保存进程
            self.running_backtests[backtest_id] = process
            
            # 准备回测记录数据
            backtest_record = {
                "id": backtest_id,
                "name": backtest_params.get("name", f"回测 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"),
                "strategy_id": backtest_params.get("strategy_id", ""),
                "strategy_name": backtest_params.get("strategy_name", ""),
                "timeframe": backtest_params.get("timeframe", "1h"),
                "start_date": backtest_params["start_date"].isoformat() if isinstance(backtest_params["start_date"], datetime) else backtest_params["start_date"],
                "end_date": backtest_params["end_date"].isoformat() if isinstance(backtest_params["end_date"], datetime) else backtest_params["end_date"],
                "initial_balance": backtest_params.get("initial_balance", 10000.0),
                "commission": backtest_params.get("commission", 0.1),
                "slippage": backtest_params.get("slippage", 0.05),
                "max_open_trades": backtest_params.get("max_open_trades", 3),
                "stake_amount": backtest_params.get("stake_amount", 100.0),
                "status": "running",
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "config": config,
                "results": None,
                "error": None,
                "progress": 0
            }
            
            # 注意：不再这里创建记录，记录已在API层创建
            # await BacktestDatabase.create_backtest(backtest_record)
            
            # 启动异步任务监控回测进程
            asyncio.create_task(self._monitor_backtest_process(backtest_id, process))
            
            logger.info(f"回测已启动，ID: {backtest_id}")
            return {
                "id": backtest_id,
                "status": "running",
                "message": "回测已成功启动"
            }
        except Exception as e:
            logger.error(f"启动回测失败: {e}", exc_info=True)
            return {
                "error": str(e),
                "status": "failed",
                "message": f"启动回测失败: {str(e)}"
            }
    
    async def process_completed_backtest(self, backtest_id: str) -> bool:
        """手动处理已完成的回测结果"""
        try:
            logger.info(f"开始处理已完成的回测结果: {backtest_id}")

            # 检查结果文件是否存在（排除.meta.json文件）
            result_files = [f for f in self.backtest_results_dir.glob(f"backtest_result_{backtest_id}-*.json")
                           if not f.name.endswith('.meta.json')]
            if not result_files:
                logger.error(f"未找到回测结果文件: {backtest_id}")
                return False

            result_file = result_files[0]  # 取第一个匹配的文件
            logger.info(f"找到结果文件: {result_file}")

            # 读取结果文件
            with open(result_file, 'r', encoding='utf-8') as f:
                result_data = json.load(f)

            # 解析结果
            strategy_name = list(result_data['strategy'].keys())[0]
            strategy_data = result_data['strategy'][strategy_name]

            # 更新数据库
            update_data = {
                "status": "completed",
                "total_trades": strategy_data.get("total_trades", 0),
                "total_profit": strategy_data.get("profit_total_abs", 0),
                "total_profit_pct": strategy_data.get("profit_total", 0),
                "max_drawdown": strategy_data.get("max_drawdown_abs", 0),
                "sharpe_ratio": strategy_data.get("sharpe", 0),
                "final_balance": strategy_data.get("final_balance", 0),
                "result_path": str(result_file),
                "updated_at": datetime.now()
            }

            await BacktestDatabase.update_backtest(backtest_id, update_data)
            logger.info(f"回测结果处理完成: {backtest_id}")

            # 从运行中的进程列表中移除
            if backtest_id in self.running_backtests:
                del self.running_backtests[backtest_id]

            return True

        except Exception as e:
            logger.error(f"处理回测结果时出错: {e}", exc_info=True)
            return False

    async def cleanup_stuck_processes(self) -> int:
        """清理卡住的回测进程"""
        cleaned_count = 0
        try:
            logger.info("开始清理卡住的回测进程...")

            # 获取所有回测，然后筛选运行中的
            all_backtests = await BacktestDatabase.get_all_backtests()
            running_backtests = [bt for bt in all_backtests if bt.get("status") == "running"]

            for backtest in running_backtests:
                backtest_id = backtest["id"]
                created_at = backtest.get("created_at")

                # 检查回测是否运行时间过长（超过30分钟）
                if created_at:
                    if isinstance(created_at, str):
                        created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    else:
                        created_time = created_at

                    runtime = datetime.now() - created_time.replace(tzinfo=None)

                    if runtime.total_seconds() > 1800:  # 超过30分钟
                        logger.warning(f"发现卡住的回测进程: {backtest_id}, 运行时间: {int(runtime.total_seconds())}秒")

                        # 尝试停止进程
                        if backtest_id in self.running_backtests:
                            try:
                                process = self.running_backtests[backtest_id]
                                process.kill()
                                await process.wait()
                                del self.running_backtests[backtest_id]
                                logger.info(f"已强制终止卡住的进程: {backtest_id}")
                            except Exception as e:
                                logger.error(f"终止卡住进程时出错: {e}")

                        # 更新数据库状态
                        await BacktestDatabase.update_backtest(backtest_id, {
                            "status": "failed",
                            "error": f"回测进程卡住（运行时间超过30分钟），已被清理",
                            "updated_at": datetime.now()
                        })

                        cleaned_count += 1

            if cleaned_count > 0:
                logger.info(f"清理完成，共清理了 {cleaned_count} 个卡住的进程")
            else:
                logger.info("没有发现卡住的进程")

            return cleaned_count

        except Exception as e:
            logger.error(f"清理卡住进程时出错: {e}", exc_info=True)
            return 0

    async def stop_backtest(self, backtest_id: str) -> bool:
        """停止运行中的回测"""
        try:
            backtest = await BacktestDatabase.get_backtest_by_id(backtest_id)
            if not backtest:
                logger.warning(f"回测不存在: {backtest_id}")
                return False
            
            if backtest["status"] == "running":
                logger.info(f"正在停止回测: {backtest_id}")
                
                # 检查进程是否存在
                if backtest_id in self.running_backtests:
                    process = self.running_backtests[backtest_id]
                    
                    try:
                        # 首先尝试使用SIGINT信号终止进程（相当于Ctrl+C）
                        logger.info(f"发送SIGINT信号尝试停止回测: {backtest_id}")
                        process.send_signal(signal.SIGINT)
                        
                        # 等待进程响应（最多5秒）
                        try:
                            await asyncio.wait_for(process.wait(), 5.0)
                            logger.info(f"回测进程已正常终止: {backtest_id}")
                        except asyncio.TimeoutError:
                            # 如果SIGINT没有终止进程，尝试SIGTERM
                            logger.warning(f"SIGINT信号未能终止进程，尝试SIGTERM: {backtest_id}")
                            process.send_signal(signal.SIGTERM)
                            
                            try:
                                await asyncio.wait_for(process.wait(), 3.0)
                                logger.info(f"回测进程已通过SIGTERM终止: {backtest_id}")
                            except asyncio.TimeoutError:
                                # 如果SIGTERM也没有终止进程，使用SIGKILL
                                logger.warning(f"SIGTERM信号未能终止进程，尝试SIGKILL: {backtest_id}")
                                process.kill()
                                await process.wait()
                                logger.info(f"回测进程已被强制终止: {backtest_id}")
                    except ProcessLookupError:
                        logger.info(f"回测进程已不存在: {backtest_id}")
                    except Exception as e:
                        logger.error(f"终止回测进程时出错: {e}", exc_info=True)
                    
                    # 从运行列表中移除
                    del self.running_backtests[backtest_id]
                else:
                    logger.warning(f"回测进程不在运行列表中: {backtest_id}")
                
                # 更新数据库状态
                await BacktestDatabase.update_backtest(backtest_id, {
                    "status": "cancelled",
                    "updated_at": datetime.now().isoformat()
                })
                
                logger.info(f"回测已取消: {backtest_id}")
                return True
            else:
                logger.warning(f"回测不在运行状态，无需停止: {backtest_id}, 当前状态: {backtest['status']}")
                return False
        except Exception as e:
            logger.error(f"停止回测失败: {e}", exc_info=True)
            return False
    
    async def _monitor_backtest_process(self, backtest_id: str, process: asyncio.subprocess.Process):
        """监控回测进程 - 实时日志记录"""
        try:
            logger.info(f"开始监控回测进程，ID: {backtest_id}")
            
            # 创建日志文件路径
            stdout_log_path = self.backtest_results_dir / f"stdout_{backtest_id}.log"
            stderr_log_path = self.backtest_results_dir / f"stderr_{backtest_id}.log"
            
            # 创建进度日志文件
            progress_log_path = self.backtest_results_dir / f"progress_{backtest_id}.log"
            
            # 初始化日志文件
            with open(stdout_log_path, 'w', encoding='utf-8') as f:
                f.write(f"=== 回测开始时间: {datetime.now()} ===\n")
                f.write(f"回测ID: {backtest_id}\n")
                f.write(f"进程ID: {process.pid}\n")
                f.write("=== 执行日志 ===\n")
            
            with open(progress_log_path, 'w', encoding='utf-8') as f:
                f.write(f"回测任务启动: {datetime.now()}\n")
                f.write(f"状态: 正在初始化...\n")
            
            # 实时读取输出
            stdout_lines = []
            stderr_lines = []
            last_progress_update = datetime.now()
            start_time = datetime.now()

            # 设置超时时间（30分钟）
            TIMEOUT_SECONDS = 30 * 60

            async def read_stdout():
                """实时读取标准输出"""
                async for line in process.stdout:
                    try:
                        line_text = line.decode('utf-8', errors='replace').rstrip()
                        stdout_lines.append(line_text)
                        
                        # 实时写入文件
                        with open(stdout_log_path, 'a', encoding='utf-8') as f:
                            f.write(f"{datetime.now().strftime('%H:%M:%S')} | {line_text}\n")
                        
                        # 检查是否是进度相关信息
                        if any(keyword in line_text.lower() for keyword in [
                            'loading', 'processing', 'calculating', 'analyzing', 
                            'backtesting', 'strategy', 'timeframe', 'data',
                            'pair', 'trades', 'result', 'complete', 'done'
                        ]):
                            # 更新进度日志
                            with open(progress_log_path, 'a', encoding='utf-8') as f:
                                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {line_text}\n")
                            
                            # 记录到后端日志
                            logger.info(f"回测进度更新 [{backtest_id}]: {line_text}")
                            
                    except Exception as e:
                        logger.warning(f"处理输出行时出错: {e}")
            
            async def read_stderr():
                """实时读取标准错误"""
                async for line in process.stderr:
                    try:
                        line_text = line.decode('utf-8', errors='replace').rstrip()
                        stderr_lines.append(line_text)
                        
                        # 实时写入错误日志文件
                        with open(stderr_log_path, 'a', encoding='utf-8') as f:
                            f.write(f"{datetime.now().strftime('%H:%M:%S')} | {line_text}\n")
                        
                        # 记录stderr输出，区分真正的错误和信息
                        if line_text.strip():
                            # 检查是否是真正的错误
                            if any(error_keyword in line_text.lower() for error_keyword in [
                                'error', 'exception', 'traceback', 'failed', 'could not'
                            ]):
                                logger.error(f"回测错误 [{backtest_id}]: {line_text}")
                            else:
                                # 这些可能是INFO或DEBUG信息，记录为INFO级别
                                logger.info(f"回测信息 [{backtest_id}]: {line_text}")
                            
                    except Exception as e:
                        logger.warning(f"处理错误输出行时出错: {e}")
            
            async def update_progress():
                """定期更新进度信息并检查超时"""
                nonlocal last_progress_update

                while True:
                    try:
                        await asyncio.sleep(10)  # 每10秒更新一次
                        current_time = datetime.now()

                        # 检查进程是否还在运行
                        if process.returncode is not None:
                            logger.info(f"检测到进程已终止 [{backtest_id}]: 返回码 {process.returncode}")
                            break

                        # 计算运行时间
                        runtime = current_time - start_time
                        total_seconds = int(runtime.total_seconds())

                        # 检查是否超时
                        if total_seconds > TIMEOUT_SECONDS:
                            logger.warning(f"回测进程超时 [{backtest_id}]: 运行时间超过{TIMEOUT_SECONDS}秒，强制终止")

                            # 更新进度日志
                            with open(progress_log_path, 'a', encoding='utf-8') as f:
                                f.write(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')} | 状态: 回测超时，正在终止进程...\n")

                            # 强制终止进程
                            try:
                                process.terminate()
                                await asyncio.sleep(5)  # 等待5秒
                                if process.returncode is None:
                                    process.kill()  # 如果还没终止，强制杀死
                                    await asyncio.sleep(2)
                            except Exception as e:
                                logger.error(f"终止超时进程时出错: {e}")

                            # 更新数据库状态
                            await BacktestDatabase.update_backtest(backtest_id, {
                                "status": "failed",
                                "error": f"回测超时（运行时间超过{TIMEOUT_SECONDS}秒）",
                                "updated_at": datetime.now()
                            })
                            break

                        # 检查是否有结果文件生成（可能进程卡住但结果已完成）
                        if total_seconds > 60:  # 运行超过1分钟后开始检查
                            result_files = list(self.backtest_results_dir.glob(f"backtest_result_{backtest_id}-*.json"))
                            main_result_files = [f for f in result_files if not f.name.endswith('.meta.json')]

                            if main_result_files:
                                try:
                                    # 检查结果文件是否完整
                                    result_file = main_result_files[0]
                                    with open(result_file, 'r', encoding='utf-8') as f:
                                        result_data = json.load(f)

                                    if 'strategy' in result_data and result_data['strategy']:
                                        logger.info(f"检测到完整结果文件，但进程未退出 [{backtest_id}]: {result_file}")

                                        # 更新进度日志
                                        with open(progress_log_path, 'a', encoding='utf-8') as f:
                                            f.write(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')} | 状态: 检测到结果文件，强制终止卡住的进程\n")

                                        # 强制终止卡住的进程
                                        try:
                                            process.terminate()
                                            await asyncio.sleep(3)
                                            if process.returncode is None:
                                                process.kill()
                                        except Exception as e:
                                            logger.warning(f"终止卡住的回测进程时出错: {e}")

                                        break

                                except (json.JSONDecodeError, FileNotFoundError, KeyError) as e:
                                    logger.debug(f"结果文件检查失败 [{backtest_id}]: {e}")
                                except Exception as e:
                                    logger.warning(f"检查结果文件时出错 [{backtest_id}]: {e}")

                        # 更新进度日志
                        with open(progress_log_path, 'a', encoding='utf-8') as f:
                            f.write(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')} | 状态: 正在执行回测... (运行时间: {total_seconds}秒)\n")

                        # 记录到后端日志
                        logger.info(f"回测运行状态 [{backtest_id}]: 正在执行中... (运行时间: {total_seconds}秒)")

                        last_progress_update = current_time

                    except Exception as e:
                        logger.warning(f"更新进度时出错: {e}")
                        break
            
            # 并发执行读取任务，添加超时控制
            try:
                await asyncio.wait_for(
                    asyncio.gather(
                        read_stdout(),
                        read_stderr(),
                        update_progress(),
                        process.wait()
                    ),
                    timeout=TIMEOUT_SECONDS + 60  # 给额外1分钟的缓冲时间
                )
            except asyncio.TimeoutError:
                logger.error(f"回测进程监控超时 [{backtest_id}]，强制终止")
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass

                await BacktestDatabase.update_backtest(backtest_id, {
                    "status": "failed",
                    "error": "回测监控超时",
                    "updated_at": datetime.now()
                })
                return
            
            # 进程结束后的处理
            with open(progress_log_path, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 状态: 回测进程已完成\n")
                f.write(f"退出码: {process.returncode}\n")

            logger.info(f"回测进程已完成，ID: {backtest_id}, 退出码: {process.returncode}")

            # 检查回测是否已经在结果文件检测阶段被标记为completed
            current_backtest = await BacktestDatabase.get_backtest_by_id(backtest_id)
            if current_backtest and current_backtest.get("status") == "completed":
                logger.info(f"回测已在结果文件检测阶段完成，跳过进程结束处理 [{backtest_id}]")
                return

            # 检查进程返回码
            if process.returncode != 0:
                # 如果是被我们主动终止的（-15 SIGTERM, -9 SIGKILL），且有结果文件，则不认为是失败
                if process.returncode in [-15, -9]:
                    result_files = list(self.backtest_results_dir.glob(f"backtest_result_{backtest_id}-*.json"))
                    main_result_files = [f for f in result_files if not f.name.endswith('.meta.json')]

                    if main_result_files:
                        logger.info(f"进程被主动终止但有结果文件，认为回测成功 [{backtest_id}]")
                        # 继续处理结果文件
                    else:
                        error_msg = f"回测进程被终止且无结果文件: {process.returncode}"
                        logger.error(f"回测失败，ID: {backtest_id}, {error_msg}")
                        await BacktestDatabase.update_backtest(backtest_id, {
                            "status": "failed",
                            "error": error_msg,
                            "updated_at": datetime.now()
                        })
                        return
                else:
                    error_msg = f"回测进程返回非零退出码: {process.returncode}"
                    if stderr_lines:
                        error_msg += f"\n错误信息: {chr(10).join(stderr_lines[-10:])}"  # 显示最后10行错误

                    logger.error(f"回测失败，ID: {backtest_id}, {error_msg}")
                    await BacktestDatabase.update_backtest(backtest_id, {
                        "status": "failed",
                        "error": error_msg,
                        "updated_at": datetime.now()
                    })
                    return
            
            # 检查结果文件是否存在（支持带时间戳的文件名）
            result_files = list(self.backtest_results_dir.glob(f"backtest_result_{backtest_id}-*.json"))

            # 过滤掉 .meta.json 文件，优先选择主要的结果文件
            main_result_files = [f for f in result_files if not f.name.endswith('.meta.json')]

            if not main_result_files:
                # 尝试旧格式的文件名
                result_path = self.backtest_results_dir / f"backtest_result_{backtest_id}.json"
                if not result_path.exists():
                    logger.error(f"回测结果文件不存在，ID: {backtest_id}")
                    await BacktestDatabase.update_backtest(backtest_id, {
                        "status": "failed",
                        "error": f"回测结果文件不存在",
                        "updated_at": datetime.now()
                    })
                    return
            else:
                result_path = main_result_files[0]  # 使用找到的第一个主要结果文件
                logger.info(f"选择主要结果文件: {result_path}")
                
            # 读取结果文件
            try:
                with open(result_path, "r", encoding='utf-8') as f:
                    result_data = json.load(f)
                logger.info(f"成功读取回测结果文件，ID: {backtest_id}")

                # 提取回测结果指标
                update_data = {
                    "status": "completed",
                    "result_path": str(result_path),
                    "results": result_data,
                    "updated_at": datetime.now()
                }

                # 从结果数据中提取关键指标
                if 'strategy' in result_data and result_data['strategy']:
                    strategy_name = list(result_data['strategy'].keys())[0]
                    strategy_data = result_data['strategy'][strategy_name]

                    # 提取关键指标
                    total_trades = strategy_data.get("total_trades", 0)
                    profit_total_abs = strategy_data.get("profit_total_abs", 0)
                    profit_total_pct = strategy_data.get("profit_total", 0)
                    max_drawdown_abs = strategy_data.get("max_drawdown_abs", 0)
                    sharpe_ratio = strategy_data.get("sharpe", 0)
                    starting_balance = strategy_data.get("starting_balance", 10000.0)
                    final_balance = starting_balance + profit_total_abs

                    # 更新指标数据
                    update_data.update({
                        "total_trades": total_trades,
                        "total_profit": profit_total_abs,
                        "total_profit_pct": profit_total_pct * 100,  # 转换为百分比
                        "max_drawdown": max_drawdown_abs,
                        "sharpe_ratio": sharpe_ratio,
                        "final_balance": final_balance
                    })

                    logger.info(f"回测指标提取完成 [{backtest_id}]: 交易数={total_trades}, 总收益={profit_total_abs:.2f}, 收益率={profit_total_pct*100:.2f}%, 最终余额={final_balance:.2f}")

                # 更新数据库记录
                await BacktestDatabase.update_backtest(backtest_id, update_data)
                logger.info(f"回测完成并已更新数据库，ID: {backtest_id}")
            except Exception as e:
                logger.error(f"读取回测结果文件失败: {e}，ID: {backtest_id}")
                await BacktestDatabase.update_backtest(backtest_id, {
                    "status": "failed",
                    "error": f"读取回测结果文件失败: {str(e)}",
                    "updated_at": datetime.now()
                })
        except Exception as e:
            logger.error(f"监控回测进程时出错: {e}，ID: {backtest_id}", exc_info=True)
            await BacktestDatabase.update_backtest(backtest_id, {
                "status": "failed",
                "error": f"监控回测进程时出错: {str(e)}",
                "updated_at": datetime.now()
            })
        finally:
            # 从运行中的回测列表中移除
            if backtest_id in self.running_backtests:
                del self.running_backtests[backtest_id]
                logger.info(f"从运行中的回测列表中移除，ID: {backtest_id}")
    
    def _create_backtest_config(self, backtest_params: Dict, use_proxy: bool = True) -> Dict:
        """创建回测配置"""
        logger.info(f"创建回测配置，参数: {backtest_params}, 使用代理: {use_proxy}")
        
        # 复制配置模板
        config = self.config_template.copy() if self.config_template else {}
        
        # 更新配置
        initial_balance = backtest_params.get("initial_balance", 10000)
        max_open_trades = backtest_params.get("max_open_trades", 3)
        
        # 计算合适的stake_amount：初始余额除以最大开仓数，再乘以0.8作为安全边际
        stake_amount = (initial_balance * 0.8) / max_open_trades
        
        config["max_open_trades"] = max_open_trades
        config["stake_amount"] = stake_amount
        config["stake_currency"] = backtest_params.get("stake_currency", "USDT")
        config["dry_run_wallet"] = initial_balance

        # 设置用户选择的时间周期
        config["timeframe"] = backtest_params.get("timeframe", "5m")
        
        # 确保config有exchange键
        if "exchange" not in config:
            config["exchange"] = {}
        
        # 设置交易对
        config["exchange"]["pair_whitelist"] = backtest_params["pairs"]
        
        # 回测模式配置：保留代理但使用匿名访问
        config["exchange"]["sandbox"] = False  # 不使用沙盒
        config["exchange"]["key"] = ""  # 空密钥，使用公开API
        config["exchange"]["secret"] = ""  # 空密钥，使用公开API 
        config["exchange"]["password"] = ""  # 空密码
        
        # 根据参数决定是否使用代理设置
        if "ccxt_config" not in config["exchange"]:
            config["exchange"]["ccxt_config"] = {}
        if "ccxt_async_config" not in config["exchange"]:
            config["exchange"]["ccxt_async_config"] = {}

        # 基础网络配置
        network_config = {
            "enableRateLimit": True,
            "rateLimit": 5000,  # 降低请求频率，设置为5秒间隔
            "timeout": 30000,   # 增加超时时间
            "retries": 3,       # 减少重试次数
            "verify": False     # 禁用SSL验证
        }

        # 只有在use_proxy为True时才添加代理设置
        if use_proxy:
            network_config["proxies"] = {
                "http": "http://127.0.0.1:7897",
                "https": "http://127.0.0.1:7897"
            }
            logger.info("回测配置：使用代理设置")
        else:
            logger.info("回测配置：不使用代理设置")

        config["exchange"]["ccxt_config"].update(network_config)
        config["exchange"]["ccxt_async_config"].update(network_config)

        # 如果不使用代理，确保删除任何现有的代理设置
        if not use_proxy:
            logger.info(f"检查ccxt_config中的代理设置: {config['exchange']['ccxt_config']}")
            if "proxies" in config["exchange"]["ccxt_config"]:
                del config["exchange"]["ccxt_config"]["proxies"]
                logger.info("已从ccxt_config中删除代理设置")
            else:
                logger.info("ccxt_config中没有代理设置")

            logger.info(f"检查ccxt_async_config中的代理设置: {config['exchange']['ccxt_async_config']}")
            if "proxies" in config["exchange"]["ccxt_async_config"]:
                del config["exchange"]["ccxt_async_config"]["proxies"]
                logger.info("已从ccxt_async_config中删除代理设置")
            else:
                logger.info("ccxt_async_config中没有代理设置")
        
        # 数据目录通过命令行参数指定，不在配置文件中设置
        
        # 确保包含必要的 pricing 配置
        if "entry_pricing" not in config:
            config["entry_pricing"] = {
                "price_side": "same",
                "use_order_book": True,
                "order_book_top": 1,
                "price_last_balance": 0.0,
                "check_depth_of_market": {
                    "enabled": False,
                    "bids_to_ask_delta": 1
                }
            }
        
        if "exit_pricing" not in config:
            config["exit_pricing"] = {
                "price_side": "same",
                "use_order_book": True,
                "order_book_top": 1
            }
        
        logger.info(f"回测配置创建完成: {json.dumps(config, indent=2)}")
        return config
    
    def _build_backtest_command(self, backtest_id: str, config_path: Path, backtest_params: Dict) -> List[str]:
        """构建回测命令"""
        logger.info(f"构建回测命令，ID: {backtest_id}")
        
        # 从环境变量读取代理配置，如果没有则使用默认值
        proxy_host = os.getenv("PROXY_HOST", "127.0.0.1")
        proxy_port = os.getenv("PROXY_PORT", "7897")
        proxy_url = f"http://{proxy_host}:{proxy_port}"

        # 设置代理环境变量用于下载历史数据
        os.environ["HTTP_PROXY"] = proxy_url
        os.environ["HTTPS_PROXY"] = proxy_url
        logger.info(f"回测模式：设置代理环境变量 {proxy_url}，用于下载历史数据")
        
        # 基本命令
        cmd = [
            self.freqtrade_path,
            "backtesting",
            "--config", str(config_path),
            "--strategy", backtest_params["strategy_name"],
            "--verbose",  # 添加详细输出
            "--datadir", "user_data/data/binance/spot",  # 指定spot交易数据目录
            "--breakdown", "day",  # 按天分解结果，提供更多进度信息
            "--enable-protections"  # 启用保护机制
        ]
        
        # 添加时间范围
        # 确保start_date和end_date是datetime对象
        start_date_obj = backtest_params["start_date"]
        end_date_obj = backtest_params["end_date"]
        
        if isinstance(start_date_obj, str):
            try:
                # 尝试解析ISO格式的日期字符串
                start_date = datetime.fromisoformat(start_date_obj.replace('Z', '+00:00'))
            except ValueError:
                # 如果解析失败，尝试其他格式
                start_date = datetime.strptime(start_date_obj, "%Y-%m-%d %H:%M:%S")
        else:
            # 已经是datetime对象
            start_date = start_date_obj
            
        if isinstance(end_date_obj, str):
            try:
                # 尝试解析ISO格式的日期字符串
                end_date = datetime.fromisoformat(end_date_obj.replace('Z', '+00:00'))
            except ValueError:
                # 如果解析失败，尝试其他格式
                end_date = datetime.strptime(end_date_obj, "%Y-%m-%d %H:%M:%S")
        else:
            # 已经是datetime对象
            end_date = end_date_obj
        
        # 格式化日期为Freqtrade需要的格式
        start_str = start_date.strftime("%Y%m%d")
        end_str = end_date.strftime("%Y%m%d")
        
        # 构建时间范围字符串
        timerange = f"{start_str}-{end_str}"
        cmd.extend(["--timerange", timerange])
        
        # 添加时间框架
        cmd.extend(["--timeframe", backtest_params["timeframe"]])
        
        # 添加输出选项
        result_path = self.backtest_results_dir / f"backtest_result_{backtest_id}.json"
        cmd.extend(["--export", "trades"])
        cmd.extend(["--export-filename", str(result_path)])
        
        logger.info(f"回测命令构建完成: {' '.join(cmd)}")
        return cmd
    
    async def get_backtest_result(self, backtest_id: str) -> Dict:
        """获取回测结果"""
        try:
            logger.info(f"获取回测结果，ID: {backtest_id}")
            
            # 从数据库获取回测记录
            backtest = await BacktestDatabase.get_backtest_by_id(backtest_id)
            if not backtest:
                logger.warning(f"回测记录不存在: {backtest_id}")
                return {"error": "Backtest not found"}
            
            # 如果回测还在运行中
            if backtest["status"] == "running":
                logger.info(f"回测仍在运行中: {backtest_id}")
                return {
                    "status": "running",
                    "progress": "unknown",  # TODO: 实现进度估算
                    "id": backtest_id
                }
            
            # 如果回测已完成
            if backtest["status"] == "completed":
                # 读取结果文件
                result_path = Path(backtest.get("result_path", ""))
                if result_path.exists():
                    try:
                        with open(result_path, "r") as f:
                            result_data = json.load(f)
                            logger.info(f"成功加载回测结果: {backtest_id}")
                            return {
                                "status": "completed",
                                "id": backtest_id,
                                "result": result_data
                            }
                    except Exception as e:
                        logger.error(f"读取回测结果文件失败: {e}", exc_info=True)
                        return {
                            "status": "error",
                            "id": backtest_id,
                            "error": f"Failed to read result file: {str(e)}"
                        }
                else:
                    logger.warning(f"回测结果文件不存在: {result_path}")
                    return {
                        "status": "error",
                        "id": backtest_id,
                        "error": "Result file not found"
                    }
            
            # 如果回测失败或取消
            logger.info(f"回测状态: {backtest['status']}, ID: {backtest_id}")
            return {
                "status": backtest["status"],
                "id": backtest_id
            }
        except Exception as e:
            logger.error(f"获取回测结果时出错: {e}", exc_info=True)
            return {"error": str(e)} 

    async def get_backtest_by_id(self, backtest_id: str) -> Optional[Dict]:
        """根据ID获取回测记录"""
        try:
            logger.info(f"获取回测记录，ID: {backtest_id}")
            backtest = await BacktestDatabase.get_backtest_by_id(backtest_id)
            
            if not backtest:
                return None
                
            # 为缺失的字段提供默认值
            default_fields = {
                "total_return": 0.0,
                "win_rate": 0.0,
                "winning_trades": 0,
                "losing_trades": 0,
                "avg_win": 0.0,
                "avg_loss": 0.0
            }
            
            # 更新缺失的字段
            for field, default_value in default_fields.items():
                if field not in backtest:
                    backtest[field] = default_value
            
            return backtest
        except Exception as e:
            logger.error(f"获取回测记录失败: {e}", exc_info=True)
            return None
    
    async def get_all_backtests(self) -> List[Dict]:
        """获取所有回测记录"""
        try:
            logger.info("获取所有回测记录")
            return await BacktestDatabase.get_all_backtests()
        except Exception as e:
            logger.error(f"获取所有回测记录失败: {e}", exc_info=True)
            return []
    
    async def get_backtest_results(self, backtest_id: str) -> Optional[Dict]:
        """获取回测结果"""
        try:
            logger.info(f"获取回测结果，ID: {backtest_id}")
            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                return None
                
            # 如果回测还在运行
            if backtest["status"] == "running":
                return {"status": "running", "message": "回测正在运行中"}
                
            # 如果回测已完成
            if backtest["status"] == "completed":
                # 尝试读取结果文件
                result_path = backtest.get("result_path")
                if result_path and Path(result_path).exists():
                    with open(result_path, "r") as f:
                        return json.load(f)
                else:
                    return {"status": "error", "message": "结果文件不存在"}
            
            # 其他状态
            return {"status": backtest["status"], "message": f"回测状态: {backtest['status']}"}
        except Exception as e:
            logger.error(f"获取回测结果失败: {e}", exc_info=True)
            return {"status": "error", "message": str(e)}
    
    async def get_backtest_trades(self, backtest_id: str) -> Optional[List[Dict]]:
        """获取回测交易记录"""
        try:
            logger.info(f"获取回测交易记录，ID: {backtest_id}")
            # 检查回测是否存在
            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                return None
                
            # 从数据库获取交易记录
            return await BacktestDatabase.get_trades_by_backtest_id(backtest_id)
        except Exception as e:
            logger.error(f"获取回测交易记录失败: {e}", exc_info=True)
            return []
    
    async def delete_backtest(self, backtest_id: str) -> bool:
        """删除回测记录"""
        try:
            logger.info(f"删除回测记录，ID: {backtest_id}")
            
            # 检查回测是否存在
            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                return False
                
            # 如果回测正在运行，先停止它
            if backtest["status"] == "running":
                await self.stop_backtest(backtest_id)
                
            # 删除相关文件
            result_path = backtest.get("result_path")
            if result_path and Path(result_path).exists():
                Path(result_path).unlink()
                
            config_path = self.backtest_results_dir / f"config_{backtest_id}.json"
            if config_path.exists():
                config_path.unlink()
                
            stdout_path = self.backtest_results_dir / f"stdout_{backtest_id}.log"
            if stdout_path.exists():
                stdout_path.unlink()
                
            stderr_path = self.backtest_results_dir / f"stderr_{backtest_id}.log"
            if stderr_path.exists():
                stderr_path.unlink()
                
            # 从数据库删除记录
            return await BacktestDatabase.delete_backtest(backtest_id)
        except Exception as e:
            logger.error(f"删除回测记录失败: {e}", exc_info=True)
            return False
    
    async def generate_backtest_report(self, backtest_id: str, format: str = "json") -> Optional[Dict]:
        """生成回测报告"""
        return await self.generate_report(backtest_id, format)

    async def get_backtest_logs(self, backtest_id: str, lines: int = 100) -> List[str]:
        """获取回测任务执行日志"""
        try:
            logger.info(f"获取回测日志，ID: {backtest_id}, 行数: {lines}")
            
            # 检查回测是否存在
            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                return ["回测不存在"]
            
            logs = []
            
            # 添加回测基本信息
            logs.append(f"=== 回测任务信息 ===")
            logs.append(f"回测ID: {backtest_id}")
            logs.append(f"策略名称: {backtest.get('strategy_name', 'Unknown')}")
            logs.append(f"状态: {backtest.get('status', 'Unknown')}")
            logs.append(f"创建时间: {backtest.get('created_at', 'Unknown')}")
            logs.append("")
            
            # 检查是否有进程日志文件
            stdout_log_path = self.backtest_results_dir / f"stdout_{backtest_id}.log"
            stderr_log_path = self.backtest_results_dir / f"stderr_{backtest_id}.log"
            progress_log_path = self.backtest_results_dir / f"progress_{backtest_id}.log"
            
            # 优先显示进度日志（用户最关心的）
            if progress_log_path.exists():
                logs.append("=== 任务执行进度 ===")
                try:
                    with open(progress_log_path, 'r', encoding='utf-8') as f:
                        progress_lines = f.readlines()
                        if progress_lines:
                            logs.extend([line.rstrip() for line in progress_lines])
                        else:
                            logs.append("(暂无进度信息)")
                except Exception as e:
                    logs.append(f"读取进度日志失败: {e}")
                logs.append("")
            
            # 读取标准输出日志
            if stdout_log_path.exists():
                logs.append("=== 详细执行日志 ===")
                try:
                    with open(stdout_log_path, 'r', encoding='utf-8') as f:
                        stdout_lines = f.readlines()
                        # 只获取最后N行
                        if len(stdout_lines) > lines // 2:  # 为进度日志留出空间
                            logs.append(f"... (省略前{len(stdout_lines) - lines // 2}行)")
                            stdout_lines = stdout_lines[-(lines // 2):]
                        logs.extend([line.rstrip() for line in stdout_lines])
                except Exception as e:
                    logs.append(f"读取详细日志失败: {e}")
                logs.append("")
            else:
                # 如果没有标准输出日志，提供基本信息
                if backtest.get('status') == 'running':
                    logs.append("=== 任务状态 ===")
                    logs.append("回测正在执行中...")
                    logs.append("正在准备执行环境和加载数据...")
                    logs.append("请稍等片刻，详细日志即将生成...")
                    logs.append("")
            
            # 读取标准错误日志
            if stderr_log_path.exists():
                logs.append("=== 错误/警告信息 ===")
                try:
                    with open(stderr_log_path, 'r', encoding='utf-8') as f:
                        stderr_lines = f.readlines()
                        if stderr_lines:
                            # 只显示最近的错误信息
                            if len(stderr_lines) > 10:
                                logs.append(f"... (省略前{len(stderr_lines) - 10}行)")
                                stderr_lines = stderr_lines[-10:]
                            logs.extend([line.rstrip() for line in stderr_lines])
                        else:
                            logs.append("(无错误信息)")
                except Exception as e:
                    logs.append(f"读取错误日志失败: {e}")
                logs.append("")
            
            # 如果是正在运行的回测，检查是否有进程信息
            if backtest.get('status') == 'running':
                logs.append("=== 运行状态 ===")
                if backtest_id in self.running_backtests:
                    process = self.running_backtests[backtest_id]
                    try:
                        returncode = process.returncode
                        if returncode is None:
                            logs.append("进程正在运行中...")
                        else:
                            logs.append(f"进程已结束，返回码: {returncode}")
                    except:
                        logs.append("无法获取进程状态")
                else:
                    logs.append("进程信息不可用")
                logs.append("")
            
            # 从后端日志中搜索相关信息
            try:
                backend_log_path = Path("logs/backend.log")
                if backend_log_path.exists():
                    logs.append("=== 相关后端日志 ===")
                    with open(backend_log_path, 'r', encoding='utf-8') as f:
                        backend_lines = f.readlines()
                        # 搜索包含该回测ID的日志行
                        relevant_lines = [line.rstrip() for line in backend_lines if backtest_id in line]
                        if relevant_lines:
                            # 只显示最近的一些条目
                            if len(relevant_lines) > 20:
                                logs.append(f"... (省略前{len(relevant_lines) - 20}条)")
                                relevant_lines = relevant_lines[-20:]
                            logs.extend(relevant_lines)
                        else:
                            logs.append("(未找到相关日志)")
            except Exception as e:
                logs.append(f"读取后端日志失败: {e}")
            
            return logs
            
        except Exception as e:
            logger.error(f"获取回测日志失败: {e}", exc_info=True)
            return [f"获取日志失败: {str(e)}"]

    async def generate_report(self, backtest_id: str, format: str = "json") -> Optional[Dict]:
        """生成回测报告"""
        try:
            logger.info(f"生成回测报告，ID: {backtest_id}, 格式: {format}")
            
            # 获取回测结果
            results = await self.get_backtest_results(backtest_id)
            if not results:
                return None
                
            # 获取回测记录
            backtest = await self.get_backtest_by_id(backtest_id)
            if not backtest:
                return None
                
            # 获取交易记录
            trades = await self.get_backtest_trades(backtest_id)
            
            # 生成报告
            report = {
                "backtest_id": backtest_id,
                "strategy_name": backtest.get("strategy_name", ""),
                "timeframe": backtest.get("timeframe", ""),
                "start_date": backtest.get("start_date", ""),
                "end_date": backtest.get("end_date", ""),
                "initial_balance": backtest.get("initial_balance", 0),
                "final_balance": backtest.get("final_balance", 0),
                "total_profit": backtest.get("total_profit", 0),
                "total_profit_pct": backtest.get("total_profit_pct", 0),
                "trades_count": len(trades) if trades else 0,
                "winning_trades": sum(1 for t in trades if t.get("profit", 0) > 0) if trades else 0,
                "losing_trades": sum(1 for t in trades if t.get("profit", 0) <= 0) if trades else 0,
                "results": results,
                "trades": trades,
                "generated_at": datetime.now().isoformat()
            }
            
            # 根据格式返回
            if format == "json":
                return report
            elif format == "csv":
                # 简单的CSV格式返回
                return {"csv_data": "暂不支持CSV格式", "format": "csv"}
            elif format == "html":
                # 简单的HTML格式返回
                return {"html_data": "暂不支持HTML格式", "format": "html"}
            else:
                return report
        except Exception as e:
            logger.error(f"生成回测报告失败: {e}", exc_info=True)
            return None 