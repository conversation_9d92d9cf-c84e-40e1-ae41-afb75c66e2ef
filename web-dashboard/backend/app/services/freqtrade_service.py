import asyncio
import json
import logging
import os
from typing import Dict, Any, List, Optional
import aiohttp
from datetime import datetime

logger = logging.getLogger(__name__)

class FreqtradeService:
    """Freqtrade服务类，用于与Freqtrade API交互"""
    
    def __init__(self):
        # 从环境变量或配置中获取代理设置
        self.proxy_url = os.getenv("HTTPS_PROXY") or os.getenv("HTTP_PROXY") or "http://127.0.0.1:7897"
        self.base_url = "http://localhost:8080"
        self.auth = aiohttp.BasicAuth("admin", "password")
        
        # 创建全局会话
        self.session = None
    
    async def get_session(self):
        """获取或创建aiohttp会话"""
        if self.session is None:
            # 创建带代理的连接器
            connector = aiohttp.TCPConnector(
                limit=100, 
                limit_per_host=30, 
                use_dns_cache=False,
                ssl=False  # 禁用SSL验证
            )
            
            # 创建会话并配置代理
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    "User-Agent": "OptimusDashboard/1.0"
                }
            )
            
            # 配置代理
            self.session._default_headers.update({
                "Proxy-Connection": "keep-alive"
            })
            
        return self.session
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发起API请求"""
        try:
            session = await self.get_session()
            url = f"{self.base_url}{endpoint}"
            
            # 添加认证信息
            kwargs["auth"] = self.auth
            
            # 添加代理配置
            if "proxy" not in kwargs:
                kwargs["proxy"] = self.proxy_url
            
            logger.info(f"发起请求: {method} {url}")
            
            async with session.request(method, url, **kwargs) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"请求成功: {method} {url}")
                    return data
                else:
                    error_text = await response.text()
                    logger.error(f"请求失败: {method} {url} - 状态码: {response.status}, 错误: {error_text}")
                    return {
                        "error": f"HTTP {response.status}",
                        "message": error_text
                    }
        except Exception as e:
            logger.error(f"请求异常: {method} {endpoint} - 错误: {str(e)}")
            return {
                "error": "连接错误",
                "message": str(e)
            }
    
    async def get_status(self) -> Dict[str, Any]:
        """获取Freqtrade状态"""
        return await self._make_request("GET", "/api/v1/status")
    
    async def get_trades(self) -> Dict[str, Any]:
        """获取交易记录"""
        return await self._make_request("GET", "/api/v1/trades")
    
    async def get_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        return await self._make_request("GET", "/api/v1/balance")
    
    async def get_performance(self) -> Dict[str, Any]:
        """获取性能数据"""
        return await self._make_request("GET", "/api/v1/performance")
    
    async def get_profit(self) -> Dict[str, Any]:
        """获取收益数据"""
        return await self._make_request("GET", "/api/v1/profit")
    
    async def get_open_trades(self) -> Dict[str, Any]:
        """获取未平仓交易"""
        return await self._make_request("GET", "/api/v1/open_trades")
    
    async def get_whitelist(self) -> Dict[str, Any]:
        """获取白名单交易对"""
        return await self._make_request("GET", "/api/v1/whitelist")
    
    async def close_session(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            self.session = None

# 创建全局服务实例
freqtrade_service = FreqtradeService() 