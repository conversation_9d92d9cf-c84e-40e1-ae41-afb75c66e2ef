import asyncio
import json
import logging
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
from .freqtrade_service import freqtrade_service

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.subscriptions: Dict[WebSocket, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket):
        """接受新的WebSocket连接"""
        try:
            await websocket.accept()
            self.active_connections.append(websocket)
            self.subscriptions[websocket] = set()
            logger.info(f"新的WebSocket连接建立，当前连接数: {len(self.active_connections)}")
        except Exception as e:
            logger.error(f"WebSocket连接建立失败: {e}")
            raise
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        try:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            if websocket in self.subscriptions:
                del self.subscriptions[websocket]
            logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")
        except Exception as e:
            logger.error(f"WebSocket连接断开处理失败: {e}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            if websocket in self.active_connections:
                await websocket.send_text(message)
                return True
            return False
        except WebSocketDisconnect:
            logger.info("WebSocket连接已断开")
            self.disconnect(websocket)
            return False
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
            return False
    
    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        if not self.active_connections:
            return
            
        disconnected = []
        for connection in self.active_connections.copy():
            try:
                await connection.send_text(message)
            except WebSocketDisconnect:
                logger.info("WebSocket连接在广播时断开")
                disconnected.append(connection)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for conn in disconnected:
            self.disconnect(conn)
    
    async def broadcast_to_subscribers(self, message: str, topic: str):
        """向订阅特定主题的客户端广播"""
        if not self.active_connections:
            return
            
        disconnected = []
        for connection in self.active_connections.copy():
            if topic in self.subscriptions.get(connection, set()):
                try:
                    await connection.send_text(message)
                except WebSocketDisconnect:
                    logger.info(f"WebSocket连接在发送订阅消息时断开: {topic}")
                    disconnected.append(connection)
                except Exception as e:
                    logger.error(f"发送订阅消息失败: {e}")
                    disconnected.append(connection)
        
        # 清理断开的连接
        for conn in disconnected:
            self.disconnect(conn)
    
    def subscribe(self, websocket: WebSocket, topic: str):
        """订阅主题"""
        if websocket in self.subscriptions:
            self.subscriptions[websocket].add(topic)
            logger.info(f"客户端订阅主题: {topic}")
    
    def unsubscribe(self, websocket: WebSocket, topic: str):
        """取消订阅主题"""
        if websocket in self.subscriptions:
            self.subscriptions[websocket].discard(topic)
            logger.info(f"客户端取消订阅主题: {topic}")

class WebSocketService:
    """WebSocket服务"""
    
    def __init__(self):
        self.manager = ConnectionManager()
        self.data_update_task = None
        self.update_interval = 5  # 数据更新间隔（秒）
        self.is_running = False
        self.max_retry_attempts = 3
        self.retry_delay = 1
    
    async def start_data_updates(self):
        """启动数据更新任务"""
        if self.is_running:
            return
            
        self.is_running = True
        self.data_update_task = asyncio.create_task(self._data_update_loop())
        logger.info("WebSocket数据更新任务已启动")
    
    async def stop_data_updates(self):
        """停止数据更新任务"""
        self.is_running = False
        if self.data_update_task:
            self.data_update_task.cancel()
            try:
                await self.data_update_task
            except asyncio.CancelledError:
                pass
        logger.info("WebSocket数据更新任务已停止")
    
    async def _data_update_loop(self):
        """数据更新循环"""
        retry_count = 0
        while self.is_running:
            try:
                # 检查是否有活跃连接
                if not self.manager.active_connections:
                    await asyncio.sleep(self.update_interval)
                    continue
                
                # 更新交易状态
                await self._update_trading_status()
                
                # 更新交易指标
                await self._update_trading_metrics()
                
                # 更新系统状态
                await self._update_system_status()
                
                # 重置重试计数
                retry_count = 0
                
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"数据更新循环错误: {e}")
                retry_count += 1
                
                if retry_count >= self.max_retry_attempts:
                    logger.error(f"数据更新连续失败{self.max_retry_attempts}次，重置重试计数")
                    retry_count = 0
                
                await asyncio.sleep(self.retry_delay * retry_count)
    
    async def _update_trading_status(self):
        """更新交易状态"""
        try:
            # 获取开仓交易
            open_trades = await freqtrade_service.get_open_trades()
            
            # 获取最新交易
            recent_trades = await freqtrade_service.get_trades(limit=10)
            
            message = {
                "type": "trading_status",
                "data": {
                    "open_trades": open_trades,
                    "recent_trades": recent_trades,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await self.manager.broadcast_to_subscribers(
                json.dumps(message), "trading_status"
            )
            
        except Exception as e:
            logger.error(f"更新交易状态失败: {e}")
    
    async def _update_trading_metrics(self):
        """更新交易指标"""
        try:
            # 获取盈利统计
            profit_data = await freqtrade_service.get_profit()
            
            # 获取性能统计
            performance_data = await freqtrade_service.get_performance()
            
            # 计算交易指标
            metrics = await freqtrade_service.calculate_trading_metrics()
            
            message = {
                "type": "trading_metrics",
                "data": {
                    "profit": profit_data,
                    "performance": performance_data,
                    "metrics": metrics,  # metrics已经是字典，不需要调用.dict()
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await self.manager.broadcast_to_subscribers(
                json.dumps(message), "trading_metrics"
            )
            
        except Exception as e:
            logger.error(f"更新交易指标失败: {e}")
    
    async def _update_system_status(self):
        """更新系统状态"""
        try:
            # 检查Freqtrade连接状态
            is_connected = await freqtrade_service.ping()
            
            # 获取系统状态
            if is_connected:
                status = await freqtrade_service.get_status()
                balance = await freqtrade_service.get_balance()
            else:
                status = {"state": "offline"}
                balance = {}
            
            message = {
                "type": "system_status",
                "data": {
                    "connected": is_connected,
                    "status": status,
                    "balance": balance,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await self.manager.broadcast_to_subscribers(
                json.dumps(message), "system_status"
            )
            
        except Exception as e:
            logger.error(f"更新系统状态失败: {e}")
    
    async def handle_message(self, websocket: WebSocket, data: dict):
        """处理WebSocket消息"""
        try:
            message_type = data.get("type")
            
            if message_type == "subscribe":
                topic = data.get("topic")
                if topic:
                    self.manager.subscribe(websocket, topic)
                    response = {
                        "type": "subscription_confirmed",
                        "topic": topic,
                        "timestamp": datetime.now().isoformat()
                    }
                    await self.manager.send_personal_message(
                        json.dumps(response), websocket
                    )
            
            elif message_type == "unsubscribe":
                topic = data.get("topic")
                if topic:
                    self.manager.unsubscribe(websocket, topic)
                    response = {
                        "type": "unsubscription_confirmed",
                        "topic": topic,
                        "timestamp": datetime.now().isoformat()
                    }
                    await self.manager.send_personal_message(
                        json.dumps(response), websocket
                    )
            
            elif message_type == "ping":
                response = {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }
                await self.manager.send_personal_message(
                    json.dumps(response), websocket
                )
            
            else:
                logger.warning(f"未知的消息类型: {message_type}")
                
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
            error_response = {
                "type": "error",
                "message": "消息处理失败",
                "timestamp": datetime.now().isoformat()
            }
            await self.manager.send_personal_message(
                json.dumps(error_response), websocket
            )
    
    async def send_notification(self, message: str, level: str = "info"):
        """发送通知"""
        try:
            notification = {
                "type": "notification",
                "data": {
                    "message": message,
                    "level": level,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            await self.manager.broadcast(json.dumps(notification))
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")

# 全局WebSocket服务实例
websocket_service = WebSocketService() 