from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid
import json
import logging

logger = logging.getLogger(__name__)

class SettingsService:
    def __init__(self):
        # 模拟配置存储
        self.settings = self._init_default_settings()
        self.exchange_accounts = self._init_default_exchanges()
    
    def _init_default_settings(self) -> Dict[str, Any]:
        """初始化默认配置"""
        return {
            "trading": {
                "max_open_trades": 3,
                "stake_amount": 100.0,
                "stake_currency": "USDT",
                "dry_run": False,
                "timeframe": "1h",
                "minimal_roi": {
                    "0": 0.10,
                    "10": 0.05,
                    "20": 0.02,
                    "30": 0.01
                },
                "stoploss": -0.05,
                "trailing_stop": False,
                "trailing_stop_positive": 0.01,
                "trailing_stop_positive_offset": 0.02
            },
            "telegram": {
                "enabled": False,
                "token": "",
                "chat_id": "",
                "enabled_notifications": {
                    "status": True,
                    "warning": True,
                    "startup": True,
                    "buy": True,
                    "sell": True,
                    "reload_config": True,
                    "protection_trigger": True
                }
            },
            "risk": {
                "max_daily_loss_pct": 5.0,
                "max_weekly_loss_pct": 10.0,
                "stop_loss_pct": 5.0,
                "emergency_stop": False,
                "max_drawdown_pct": 15.0,
                "position_size_limit": 10.0,
                "risk_per_trade": 2.0
            },
            "api": {
                "enabled": True,
                "listen_ip_address": "0.0.0.0",
                "listen_port": 8080,
                "verbosity": "info",
                "enable_openapi": True,
                "jwt_secret_key": "your-secret-key",
                "CORS_origins": ["http://localhost:3000"],
                "username": "admin",
                "password": "password"
            },
            "notifications": {
                "email": {
                    "enabled": False,
                    "smtp_server": "",
                    "smtp_port": 587,
                    "username": "",
                    "password": "",
                    "recipients": []
                },
                "webhook": {
                    "enabled": False,
                    "url": "",
                    "format": "json",
                    "retries": 3,
                    "retry_delay": 5
                }
            },
            "performance": {
                "cache_enabled": True,
                "cache_ttl": 3600,
                "log_level": "info",
                "max_log_files": 10,
                "database_pool_size": 5,
                "enable_profiling": False,
                "gc_threshold": 1000
            }
        }
    
    def _init_default_exchanges(self) -> List[Dict[str, Any]]:
        """初始化默认交易所账户"""
        return [
            {
                "id": str(uuid.uuid4()),
                "name": "Binance 主账户",
                "exchange": "binance",
                "api_key": "your-api-key",
                "api_secret": "your-api-secret",
                "sandbox": True,
                "enabled": True,
                "created_at": datetime.now().isoformat(),
                "last_used": datetime.now().isoformat(),
                "status": "connected"
            }
        ]
    
    async def get_all_settings(self) -> Dict[str, Any]:
        """获取所有系统设置"""
        try:
            return {
                "settings": self.settings,
                "exchange_accounts": self.exchange_accounts,
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取所有设置失败: {e}")
            raise
    
    async def get_trading_settings(self) -> Dict[str, Any]:
        """获取交易设置"""
        try:
            return self.settings["trading"]
        except Exception as e:
            logger.error(f"获取交易设置失败: {e}")
            raise
    
    async def update_trading_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """更新交易设置"""
        try:
            # 验证必要字段
            required_fields = ["max_open_trades", "stake_amount", "stake_currency"]
            for field in required_fields:
                if field not in new_settings:
                    raise ValueError(f"缺少必要字段: {field}")
            
            # 更新设置
            self.settings["trading"].update(new_settings)
            
            return self.settings["trading"]
        except Exception as e:
            logger.error(f"更新交易设置失败: {e}")
            raise
    
    async def get_telegram_settings(self) -> Dict[str, Any]:
        """获取Telegram设置"""
        try:
            # 隐藏敏感信息
            settings = self.settings["telegram"].copy()
            if settings.get("token"):
                settings["token"] = "*" * 20
            return settings
        except Exception as e:
            logger.error(f"获取Telegram设置失败: {e}")
            raise
    
    async def update_telegram_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """更新Telegram设置"""
        try:
            # 如果token是掩码，不更新
            if new_settings.get("token") and new_settings["token"].startswith("*"):
                new_settings.pop("token")
            
            self.settings["telegram"].update(new_settings)
            
            # 返回时隐藏敏感信息
            settings = self.settings["telegram"].copy()
            if settings.get("token"):
                settings["token"] = "*" * 20
            return settings
        except Exception as e:
            logger.error(f"更新Telegram设置失败: {e}")
            raise
    
    async def get_risk_settings(self) -> Dict[str, Any]:
        """获取风险管理设置"""
        try:
            return self.settings["risk"]
        except Exception as e:
            logger.error(f"获取风险管理设置失败: {e}")
            raise
    
    async def update_risk_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """更新风险管理设置"""
        try:
            # 验证风险参数
            if "max_daily_loss_pct" in new_settings and new_settings["max_daily_loss_pct"] > 20:
                raise ValueError("每日最大亏损不能超过20%")
            
            if "max_weekly_loss_pct" in new_settings and new_settings["max_weekly_loss_pct"] > 30:
                raise ValueError("每周最大亏损不能超过30%")
            
            self.settings["risk"].update(new_settings)
            return self.settings["risk"]
        except Exception as e:
            logger.error(f"更新风险管理设置失败: {e}")
            raise
    
    async def get_api_settings(self) -> Dict[str, Any]:
        """获取API设置"""
        try:
            # 隐藏敏感信息
            settings = self.settings["api"].copy()
            if settings.get("jwt_secret_key"):
                settings["jwt_secret_key"] = "*" * 20
            if settings.get("password"):
                settings["password"] = "*" * 8
            return settings
        except Exception as e:
            logger.error(f"获取API设置失败: {e}")
            raise
    
    async def update_api_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """更新API设置"""
        try:
            # 处理掩码字段
            for field in ["jwt_secret_key", "password"]:
                if new_settings.get(field) and new_settings[field].startswith("*"):
                    new_settings.pop(field)
            
            self.settings["api"].update(new_settings)
            
            # 返回时隐藏敏感信息
            settings = self.settings["api"].copy()
            if settings.get("jwt_secret_key"):
                settings["jwt_secret_key"] = "*" * 20
            if settings.get("password"):
                settings["password"] = "*" * 8
            return settings
        except Exception as e:
            logger.error(f"更新API设置失败: {e}")
            raise
    
    async def get_notification_settings(self) -> Dict[str, Any]:
        """获取通知设置"""
        try:
            # 隐藏敏感信息
            settings = self.settings["notifications"].copy()
            if settings["email"].get("password"):
                settings["email"]["password"] = "*" * 8
            return settings
        except Exception as e:
            logger.error(f"获取通知设置失败: {e}")
            raise
    
    async def update_notification_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """更新通知设置"""
        try:
            # 处理嵌套结构
            if "email" in new_settings:
                if new_settings["email"].get("password") and new_settings["email"]["password"].startswith("*"):
                    new_settings["email"].pop("password")
                self.settings["notifications"]["email"].update(new_settings["email"])
            
            if "webhook" in new_settings:
                self.settings["notifications"]["webhook"].update(new_settings["webhook"])
            
            # 返回时隐藏敏感信息
            settings = self.settings["notifications"].copy()
            if settings["email"].get("password"):
                settings["email"]["password"] = "*" * 8
            return settings
        except Exception as e:
            logger.error(f"更新通知设置失败: {e}")
            raise
    
    async def get_performance_settings(self) -> Dict[str, Any]:
        """获取性能设置"""
        try:
            return self.settings["performance"]
        except Exception as e:
            logger.error(f"获取性能设置失败: {e}")
            raise
    
    async def update_performance_settings(self, new_settings: Dict[str, Any]) -> Dict[str, Any]:
        """更新性能设置"""
        try:
            # 验证性能参数
            if "database_pool_size" in new_settings and new_settings["database_pool_size"] > 20:
                raise ValueError("数据库连接池大小不能超过20")
            
            if "cache_ttl" in new_settings and new_settings["cache_ttl"] < 60:
                raise ValueError("缓存TTL不能小于60秒")
            
            self.settings["performance"].update(new_settings)
            return self.settings["performance"]
        except Exception as e:
            logger.error(f"更新性能设置失败: {e}")
            raise
    
    async def get_exchange_accounts(self) -> List[Dict[str, Any]]:
        """获取交易所账户列表"""
        try:
            # 隐藏敏感信息
            accounts = []
            for account in self.exchange_accounts:
                masked_account = account.copy()
                if masked_account.get("api_key"):
                    masked_account["api_key"] = masked_account["api_key"][:8] + "*" * 20
                if masked_account.get("api_secret"):
                    masked_account["api_secret"] = "*" * 32
                accounts.append(masked_account)
            return accounts
        except Exception as e:
            logger.error(f"获取交易所账户失败: {e}")
            raise
    
    async def add_exchange_account(self, account: Dict[str, Any]) -> Dict[str, Any]:
        """添加交易所账户"""
        try:
            # 验证必要字段
            required_fields = ["name", "exchange", "api_key", "api_secret"]
            for field in required_fields:
                if not account.get(field):
                    raise ValueError(f"缺少必要字段: {field}")
            
            new_account = {
                "id": str(uuid.uuid4()),
                "name": account["name"],
                "exchange": account["exchange"],
                "api_key": account["api_key"],
                "api_secret": account["api_secret"],
                "sandbox": account.get("sandbox", True),
                "enabled": account.get("enabled", True),
                "created_at": datetime.now().isoformat(),
                "last_used": None,
                "status": "not_tested"
            }
            
            self.exchange_accounts.append(new_account)
            
            # 返回时隐藏敏感信息
            masked_account = new_account.copy()
            masked_account["api_key"] = masked_account["api_key"][:8] + "*" * 20
            masked_account["api_secret"] = "*" * 32
            
            return masked_account
        except Exception as e:
            logger.error(f"添加交易所账户失败: {e}")
            raise
    
    async def update_exchange_account(self, account_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新交易所账户"""
        try:
            for i, account in enumerate(self.exchange_accounts):
                if account["id"] == account_id:
                    # 处理掩码字段
                    if updates.get("api_key") and "*" in updates["api_key"]:
                        updates.pop("api_key")
                    if updates.get("api_secret") and updates["api_secret"].startswith("*"):
                        updates.pop("api_secret")
                    
                    self.exchange_accounts[i].update(updates)
                    
                    # 返回时隐藏敏感信息
                    masked_account = self.exchange_accounts[i].copy()
                    masked_account["api_key"] = masked_account["api_key"][:8] + "*" * 20
                    masked_account["api_secret"] = "*" * 32
                    
                    return masked_account
            
            return None
        except Exception as e:
            logger.error(f"更新交易所账户失败: {e}")
            raise
    
    async def delete_exchange_account(self, account_id: str) -> bool:
        """删除交易所账户"""
        try:
            for i, account in enumerate(self.exchange_accounts):
                if account["id"] == account_id:
                    del self.exchange_accounts[i]
                    return True
            return False
        except Exception as e:
            logger.error(f"删除交易所账户失败: {e}")
            raise
    
    async def test_freqtrade_connection(self) -> Dict[str, Any]:
        """测试Freqtrade连接"""
        try:
            # 模拟连接测试
            import random
            
            if random.random() > 0.2:  # 80%成功率
                return {
                    "success": True,
                    "message": "连接成功",
                    "response_time": round(random.uniform(50, 200), 1),
                    "version": "2024.1",
                    "status": "running"
                }
            else:
                return {
                    "success": False,
                    "message": "连接失败",
                    "error": "连接超时或认证失败",
                    "response_time": None
                }
        except Exception as e:
            logger.error(f"测试Freqtrade连接失败: {e}")
            return {
                "success": False,
                "message": "测试失败",
                "error": str(e),
                "response_time": None
            }
    
    async def reset_settings_section(self, section: str) -> Dict[str, Any]:
        """重置指定配置节"""
        try:
            default_settings = self._init_default_settings()
            if section in default_settings:
                self.settings[section] = default_settings[section].copy()
                return self.settings[section]
            else:
                raise ValueError(f"未知的配置节: {section}")
        except Exception as e:
            logger.error(f"重置配置节失败: {e}")
            raise
    
    async def backup_settings(self) -> Dict[str, Any]:
        """备份所有设置"""
        try:
            return {
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0",
                "settings": self.settings,
                "exchange_accounts": [
                    {**account, "api_key": "REDACTED", "api_secret": "REDACTED"}
                    for account in self.exchange_accounts
                ]
            }
        except Exception as e:
            logger.error(f"备份设置失败: {e}")
            raise
    
    async def restore_settings(self, backup_data: Dict[str, Any]) -> Dict[str, Any]:
        """恢复设置"""
        try:
            if "settings" in backup_data:
                self.settings = backup_data["settings"]
            
            if "exchange_accounts" in backup_data:
                # 恢复时不恢复敏感信息
                accounts = backup_data["exchange_accounts"]
                for account in accounts:
                    if account.get("api_key") == "REDACTED":
                        account.pop("api_key", None)
                    if account.get("api_secret") == "REDACTED":
                        account.pop("api_secret", None)
                
                self.exchange_accounts = accounts
            
            return await self.get_all_settings()
        except Exception as e:
            logger.error(f"恢复设置失败: {e}")
            raise 