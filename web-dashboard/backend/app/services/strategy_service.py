import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import uuid

logger = logging.getLogger(__name__)

class StrategyService:
    """策略管理服务"""
    
    def __init__(self):
        # 模拟策略数据存储
        self._strategies = {
            "1": {
                "id": "1",
                "name": "SimpleModel3Strategy",
                "description": "基于技术指标的简单套利策略",
                "status": "active",
                "type": "arbitrage",
                "performance": {
                    "totalReturn": 12.5,
                    "sharpeRatio": 1.8,
                    "maxDrawdown": -5.2,
                    "winRate": 68.5
                },
                "config": {
                    "timeframe": "5m",
                    "minRoi": 0.02,
                    "stoploss": -0.05,
                    "trailing": True
                },
                "createdAt": "2024-01-15",
                "lastUpdated": "2024-01-20"
            },
            "2": {
                "id": "2",
                "name": "EMACrossStrategy",
                "description": "EMA均线交叉策略",
                "status": "inactive",
                "type": "technical",
                "performance": {
                    "totalReturn": 8.3,
                    "sharpeRatio": 1.2,
                    "maxDrawdown": -8.1,
                    "winRate": 55.2
                },
                "config": {
                    "timeframe": "15m",
                    "minRoi": 0.015,
                    "stoploss": -0.08,
                    "trailing": False
                },
                "createdAt": "2024-01-10",
                "lastUpdated": "2024-01-18"
            }
        }
    
    async def get_all_strategies(self) -> List[Dict[str, Any]]:
        """获取所有策略列表"""
        try:
            return list(self._strategies.values())
        except Exception as e:
            logger.error(f"获取策略列表失败: {e}")
            return []
    
    async def get_strategy_by_id(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取策略"""
        try:
            return self._strategies.get(strategy_id)
        except Exception as e:
            logger.error(f"获取策略失败: {e}")
            return None
    
    async def create_strategy(self, strategy_data: Any) -> Dict[str, Any]:
        """创建新策略"""
        try:
            strategy_id = str(uuid.uuid4())
            new_strategy = {
                "id": strategy_id,
                "name": strategy_data.name,
                "description": strategy_data.description,
                "status": "inactive",
                "type": strategy_data.type,
                "performance": {
                    "totalReturn": 0.0,
                    "sharpeRatio": 0.0,
                    "maxDrawdown": 0.0,
                    "winRate": 0.0
                },
                "config": strategy_data.config,
                "createdAt": datetime.now().strftime("%Y-%m-%d"),
                "lastUpdated": datetime.now().strftime("%Y-%m-%d")
            }
            self._strategies[strategy_id] = new_strategy
            return new_strategy
        except Exception as e:
            logger.error(f"创建策略失败: {e}")
            raise
    
    async def update_strategy(self, strategy_id: str, strategy_data: Any) -> Optional[Dict[str, Any]]:
        """更新策略"""
        try:
            if strategy_id not in self._strategies:
                return None
            
            strategy = self._strategies[strategy_id]
            strategy["name"] = strategy_data.name
            strategy["description"] = strategy_data.description
            strategy["type"] = strategy_data.type
            strategy["config"] = strategy_data.config
            strategy["lastUpdated"] = datetime.now().strftime("%Y-%m-%d")
            
            return strategy
        except Exception as e:
            logger.error(f"更新策略失败: {e}")
            return None
    
    async def update_strategy_status(self, strategy_id: str, status: str) -> bool:
        """更新策略状态"""
        try:
            if strategy_id not in self._strategies:
                return False
            
            self._strategies[strategy_id]["status"] = status
            self._strategies[strategy_id]["lastUpdated"] = datetime.now().strftime("%Y-%m-%d")
            return True
        except Exception as e:
            logger.error(f"更新策略状态失败: {e}")
            return False
    
    async def delete_strategy(self, strategy_id: str) -> bool:
        """删除策略"""
        try:
            if strategy_id not in self._strategies:
                return False
            
            del self._strategies[strategy_id]
            return True
        except Exception as e:
            logger.error(f"删除策略失败: {e}")
            return False
    
    async def get_strategy_performance(self, strategy_id: str, days: int = 30) -> Optional[Dict[str, Any]]:
        """获取策略性能数据"""
        try:
            if strategy_id not in self._strategies:
                return None
            
            strategy = self._strategies[strategy_id]
            # 模拟性能数据
            return {
                "strategy_id": strategy_id,
                "strategy_name": strategy["name"],
                "period_days": days,
                "performance": strategy["performance"],
                "equity_curve": [
                    {"date": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d"), 
                     "value": 10000 + i * 50 + (i % 7) * 20} 
                    for i in range(days)
                ]
            }
        except Exception as e:
            logger.error(f"获取策略性能失败: {e}")
            return None
    
    async def start_backtest(self, strategy_id: str, config: Dict[str, Any]) -> str:
        """启动策略回测"""
        try:
            if strategy_id not in self._strategies:
                raise ValueError("策略不存在")
            
            # 生成回测ID
            backtest_id = str(uuid.uuid4())
            logger.info(f"启动策略 {strategy_id} 的回测，回测ID: {backtest_id}")
            
            return backtest_id
        except Exception as e:
            logger.error(f"启动回测失败: {e}")
            raise
    
    async def get_strategy_trades(self, strategy_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取策略交易记录"""
        try:
            if strategy_id not in self._strategies:
                return []
            
            # 模拟交易数据
            trades = []
            for i in range(min(limit, 20)):
                trades.append({
                    "id": str(uuid.uuid4()),
                    "strategy_id": strategy_id,
                    "pair": "BTC/USDT",
                    "side": "buy" if i % 2 == 0 else "sell",
                    "amount": round(0.001 + (i % 5) * 0.0005, 6),
                    "price": 45000 + (i % 1000),
                    "profit": round((i % 10 - 5) * 10.5, 2),
                    "profit_percentage": round((i % 10 - 5) * 0.5, 2),
                    "open_date": (datetime.now() - timedelta(hours=i)).isoformat(),
                    "close_date": (datetime.now() - timedelta(hours=i-1)).isoformat() if i % 2 == 0 else None,
                    "status": "closed" if i % 2 == 0 else "open"
                })
            
            return trades
        except Exception as e:
            logger.error(f"获取策略交易记录失败: {e}")
            return []
    
    async def get_available_strategies(self) -> List[str]:
        """获取可用策略列表"""
        try:
            from app.core.strategy_registry import strategy_registry

            # 首先尝试加载策略文件以触发注册
            await self._load_strategy_files()

            # 从注册器获取策略列表
            strategies = strategy_registry.get_strategy_names()

            if not strategies:
                logger.warning("未找到任何已注册的策略")
            else:
                logger.info(f"找到 {len(strategies)} 个已注册的策略: {strategies}")

            return strategies

        except Exception as e:
            logger.error(f"获取策略列表失败: {e}")
            return []

    async def _load_strategy_files(self):
        """加载策略文件以触发注册"""
        try:
            import importlib.util
            from pathlib import Path

            # 策略文件夹路径
            strategies_dir = Path(__file__).parent.parent.parent / "user_data" / "strategies"

            if not strategies_dir.exists():
                logger.warning(f"策略文件夹不存在: {strategies_dir}")
                return

            # 加载策略文件
            for file_path in strategies_dir.glob("*.py"):
                if file_path.name.startswith("__"):
                    continue  # 跳过 __init__.py 等文件

                try:
                    # 动态导入模块以触发注册
                    spec = importlib.util.spec_from_file_location(file_path.stem, file_path)
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                        logger.debug(f"已加载策略文件: {file_path.name}")

                except Exception as e:
                    logger.warning(f"无法加载策略文件 {file_path.name}: {e}")
                    continue

        except Exception as e:
            logger.error(f"加载策略文件失败: {e}")

    async def get_strategy_info(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """获取策略详细信息"""
        try:
            from app.core.strategy_registry import strategy_registry

            strategy_info = strategy_registry.get_strategy(strategy_name)
            if strategy_info:
                return {
                    "name": strategy_info.name,
                    "description": strategy_info.description,
                    "author": strategy_info.author,
                    "version": strategy_info.version,
                    "category": strategy_info.category,
                    "timeframes": strategy_info.timeframes,
                    "pairs": strategy_info.pairs,
                    "parameters": strategy_info.parameters
                }
            return None

        except Exception as e:
            logger.error(f"获取策略信息失败: {e}")
            return None

# 创建服务实例
strategy_service = StrategyService() 