import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from ..services.freqtrade_service import FreqtradeService

logger = logging.getLogger(__name__)

class TradingService:
    """交易业务逻辑服务"""
    
    def __init__(self):
        self.freqtrade_service = FreqtradeService()
    
    async def get_trading_overview(self) -> Dict[str, Any]:
        """获取交易概览"""
        try:
            async with self.freqtrade_service as ft:
                # 获取基础数据
                open_trades = await ft.get_open_trades()  # 直接获取开仓交易数组
                profit = await ft.get_profit()
                balance = await ft.get_balance()
                
                return {
                    "status": "online" if await ft.ping() else "offline",
                    "total_profit": profit.get("profit_all_coin", 0),
                    "total_profit_percentage": profit.get("profit_all_percent", 0),
                    "open_trades": len(open_trades),
                    "balance": balance,
                    "last_update": datetime.now()
                }
        except Exception as e:
            logger.error(f"获取交易概览失败: {e}")
            return {
                "status": "error",
                "total_profit": 0,
                "total_profit_percentage": 0,
                "open_trades": 0,
                "balance": {},
                "last_update": datetime.now(),
                "error": str(e)
            }
    
    async def get_recent_trades(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近交易记录"""
        try:
            async with self.freqtrade_service as ft:
                trades = await ft.get_trades(limit=limit)
                return trades
        except Exception as e:
            logger.error(f"获取交易记录失败: {e}")
            return []
    
    async def force_trade_action(self, action: str, **kwargs) -> Dict[str, Any]:
        """执行强制交易操作"""
        try:
            async with self.freqtrade_service as ft:
                if action == "buy":
                    return await ft.force_buy(kwargs.get("pair"), kwargs.get("price"))
                elif action == "sell":
                    return await ft.force_sell(kwargs.get("tradeid"))
                elif action == "delete":
                    return await ft.delete_trade(kwargs.get("tradeid"))
                else:
                    raise ValueError(f"不支持的操作: {action}")
        except Exception as e:
            logger.error(f"强制交易操作失败: {e}")
            raise

# 创建服务实例
trading_service = TradingService() 