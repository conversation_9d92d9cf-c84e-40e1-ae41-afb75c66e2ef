#!/usr/bin/env python3
"""
后端服务启动脚本
解决模块导入路径问题
"""

import sys
import os
import argparse
from pathlib import Path

# 解析命令行参数
parser = argparse.ArgumentParser(description='启动Freqtrade Web Dashboard后端服务')
parser.add_argument('--port', type=int, default=8081, help='服务端口号')
parser.add_argument('--host', type=str, default='0.0.0.0', help='服务主机地址')
parser.add_argument('--debug', action='store_true', help='是否启用调试模式')
args = parser.parse_args()

# 将backend目录添加到Python路径中
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# 现在可以正常导入main模块
if __name__ == "__main__":
    from main import app
    import uvicorn
    
    print(f"🚀 启动Freqtrade Web Dashboard Backend在端口 {args.port}...")
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        reload=args.debug,
        log_level="info" if not args.debug else "debug",
        access_log=True
    ) 