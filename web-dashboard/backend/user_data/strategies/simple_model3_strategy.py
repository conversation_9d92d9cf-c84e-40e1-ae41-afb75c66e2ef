"""
Simple Model3 Strategy - Freqtrade策略文件

这是一个用于Freqtrade的策略文件，实现了一个简单的移动平均线交叉策略
"""

import logging
try:
    import numpy as np
    import pandas as pd
    import pandas_ta as ta
    from pandas import DataFrame
    from freqtrade.strategy import IStrategy, IntParameter
except ImportError:
    # 如果导入失败，定义空的类和函数用于注册
    class IStrategy:
        pass
    class IntParameter:
        pass
    class DataFrame:
        pass

# 导入策略注册器
import sys
import os
# 添加后端应用路径到Python路径
backend_app_path = os.path.join(os.path.dirname(__file__), '..', '..')
if backend_app_path not in sys.path:
    sys.path.insert(0, backend_app_path)

try:
    from app.core.strategy_registry import register_strategy
except ImportError:
    # 如果导入失败，定义一个空的注册函数
    def register_strategy(*args, **kwargs):
        pass

logger = logging.getLogger(__name__)

class SimpleModel3Strategy(IStrategy):
    """
    简单的移动平均线交叉策略
    """
    # 策略参数
    minimal_roi = {
        "0": 0.01,
        "30": 0.005,
        "60": 0.0025
    }
    
    stoploss = -0.05
    timeframe = '5m'
    
    # 优化参数
    buy_ema_short = IntParameter(5, 15, default=10, space="buy")
    buy_ema_long = IntParameter(20, 50, default=30, space="buy")
    
    # 处理指标
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算技术指标
        """
        # 计算EMA
        dataframe['ema_short'] = ta.ema(dataframe['close'], length=10)
        dataframe['ema_long'] = ta.ema(dataframe['close'], length=30)
        
        # 添加RSI指标
        dataframe['rsi'] = ta.rsi(dataframe['close'], length=14)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成买入信号
        """
        dataframe.loc[
            (
                # 短期EMA上穿长期EMA
                (dataframe['ema_short'] > dataframe['ema_long']) &
                (dataframe['ema_short'].shift(1) <= dataframe['ema_long'].shift(1)) &
                (dataframe['rsi'] > 30) &  # RSI不在超卖区
                (dataframe['volume'] > 0)  # 确保有成交量
            ),
            'enter_long'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        基于技术指标生成卖出信号
        """
        dataframe.loc[
            (
                # 短期EMA下穿长期EMA
                (dataframe['ema_short'] < dataframe['ema_long']) &
                (dataframe['ema_short'].shift(1) >= dataframe['ema_long'].shift(1)) &
                (dataframe['rsi'] > 70) &  # RSI在超买区
                (dataframe['volume'] > 0)  # 确保有成交量
            ),
            'exit_long'] = 1
        
        return dataframe

# 导出给Freqtrade使用
__all__ = ["SimpleModel3Strategy"]

# 注册策略到策略注册器
register_strategy(
    name="SimpleModel3Strategy",
    description="简单的移动平均线交叉策略，使用EMA和RSI指标进行买卖信号判断",
    author="Optimus Team",
    version="1.0.0",
    category="technical",
    timeframes=["1m", "5m", "15m", "1h"],
    pairs=["BTC/USDT", "ETH/USDT", "BNB/USDT"],
    parameters={
        "ema_short_period": {"type": "int", "default": 12, "min": 5, "max": 50, "description": "短期EMA周期"},
        "ema_long_period": {"type": "int", "default": 26, "min": 20, "max": 100, "description": "长期EMA周期"},
        "rsi_period": {"type": "int", "default": 14, "min": 5, "max": 30, "description": "RSI周期"},
        "rsi_buy_threshold": {"type": "int", "default": 30, "min": 20, "max": 40, "description": "RSI买入阈值"},
        "rsi_sell_threshold": {"type": "int", "default": 70, "min": 60, "max": 80, "description": "RSI卖出阈值"}
    }
)