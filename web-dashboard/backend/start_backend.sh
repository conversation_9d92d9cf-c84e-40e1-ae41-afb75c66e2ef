#!/bin/bash

# Freqtrade Web Dashboard Backend 启动脚本
# 确保在freqtrade虚拟环境中运行

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
VENV_PATH="$PROJECT_ROOT/venv_freqtrade"

echo "🚀 启动Freqtrade Web Dashboard Backend..."
echo "项目根目录: $PROJECT_ROOT"
echo "虚拟环境路径: $VENV_PATH"

# 检查虚拟环境是否存在
if [ ! -d "$VENV_PATH" ]; then
    echo "❌ 错误: freqtrade虚拟环境不存在: $VENV_PATH"
    echo "请先创建freqtrade虚拟环境"
    exit 1
fi

# 检查虚拟环境中的Python
if [ ! -f "$VENV_PATH/bin/python" ]; then
    echo "❌ 错误: 虚拟环境中没有找到Python: $VENV_PATH/bin/python"
    exit 1
fi

# 切换到后端目录
cd "$SCRIPT_DIR"

# 检查依赖
echo "🔍 检查依赖..."
if ! "$VENV_PATH/bin/python" -c "import freqtrade, fastapi, uvicorn" 2>/dev/null; then
    echo "❌ 错误: 缺少必要的依赖包"
    echo "请确保在freqtrade虚拟环境中安装了所有依赖"
    exit 1
fi

echo "✅ 依赖检查通过"

# 设置环境变量
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# 启动后端服务
echo "🎯 使用freqtrade虚拟环境启动后端服务..."
echo "Python路径: $VENV_PATH/bin/python"
echo "工作目录: $SCRIPT_DIR"

exec "$VENV_PATH/bin/python" run.py
