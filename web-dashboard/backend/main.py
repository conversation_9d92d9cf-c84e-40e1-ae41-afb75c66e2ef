"""
Freqtrade Web Dashboard Backend
FastAPI主应用程序 - 集成WebSocket实时数据服务
"""

import asyncio
import json
import logging
import os
from pathlib import Path
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import sys

# 导入API路由
from app.api import (
    dashboard,
    auth,
    trading,
    strategies,
    backtests,
    monitoring,
    settings,
    health,
    websocket_router
)

# 导入WebSocket服务
from app.services.websocket_service import websocket_service

# 导入数据库
from app.database import connect_database, disconnect_database, create_tables

# 配置日志
# 确保日志目录存在
log_dir = Path(__file__).parent / "logs"
log_dir.mkdir(exist_ok=True)
log_file = log_dir / "backend.log"

# 配置日志格式和处理器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        # 控制台处理器
        logging.StreamHandler(),
        # 文件处理器
        logging.FileHandler(log_file)
    ]
)
logger = logging.getLogger(__name__)
logger.info(f"日志将保存到: {log_file}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    # 启动时的初始化
    logger.info("🚀 启动Web Dashboard后端服务...")
    
    # 创建数据库表
    create_tables()
    
    # 连接数据库
    await connect_database()
    
    logger.info("✅ 数据库初始化完成")
    
    yield
    
    # 关闭时的清理
    logger.info("🛑 关闭Web Dashboard后端服务...")
    await disconnect_database()

# 创建FastAPI应用
app = FastAPI(
    title="Freqtrade Web Dashboard",
    description="Freqtrade量化交易Web管理面板",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 添加Gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 注册API路由
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(dashboard.router, prefix="/api/v1/dashboard", tags=["dashboard"])
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(trading.router, prefix="/api/v1/trading", tags=["trading"])
app.include_router(strategies.router, prefix="/api/v1/strategies", tags=["strategies"])
app.include_router(backtests.router, prefix="/api/v1/backtests", tags=["backtests"])
app.include_router(monitoring.router, prefix="/api/v1/monitoring", tags=["monitoring"])
app.include_router(settings.router, prefix="/api/v1/settings", tags=["settings"])
app.include_router(websocket_router.router, prefix="/api/v1/ws", tags=["websocket"])

# 直接注册WebSocket端点
@app.websocket("/api/v1/ws")
async def websocket_endpoint(websocket: WebSocket):
    """主WebSocket端点 - 实时数据推送"""
    await websocket_service.manager.connect(websocket)
    logger.info(f"📡 WebSocket连接建立")
    
    try:
        # 发送连接成功消息
        import json
        await websocket_service.manager.send_personal_message(
            json.dumps({
                "type": "connected", 
                "message": "WebSocket connected successfully",
                "timestamp": str(asyncio.get_event_loop().time())
            }),
            websocket
        )
        
        # 消息处理循环
        while True:
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                await websocket_service.handle_message(websocket, message)
            except json.JSONDecodeError:
                await websocket_service.manager.send_personal_message(
                    json.dumps({"type": "error", "message": "Invalid JSON format"}),
                    websocket
                )
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await websocket_service.manager.send_personal_message(
                    json.dumps({
                        "type": "error", 
                        "message": f"Message processing failed: {str(e)}"
                    }),
                    websocket
                )
                
    except WebSocketDisconnect:
        websocket_service.manager.disconnect(websocket)
        logger.info("📡 WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
        websocket_service.manager.disconnect(websocket)

@app.get("/")
async def root():
    """根路径 - API信息"""
    return {
        "message": "Freqtrade Web Dashboard API", 
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": "2024-01-20T10:00:00Z"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"}
    )

if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8081,
        reload=True,
        log_level="info"
    ) 