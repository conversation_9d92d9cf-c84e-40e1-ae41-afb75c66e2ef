{"project": {"name": "Freqtrade Web Dashboard", "version": "1.0.0", "description": "基于Freqtrade的量化交易系统Web管理界面", "author": "Optimus Team", "license": "MIT"}, "services": {"backend": {"name": "FastAPI Backend", "port": 8081, "host": "localhost", "path": "web-dashboard/backend", "entry": "run.py", "python_env": "venv_freqtrade", "requirements": "requirements.txt"}, "frontend": {"name": "React Frontend", "port": 3000, "host": "localhost", "path": "web-dashboard/frontend", "entry": "npm start", "package_manager": "npm"}}, "directories": {"config": "config", "strategies": "user_data/strategies", "data": "user_data/data", "logs": "user_data/logs", "backtest_results": "web-dashboard/backend/backtest_results", "backend_logs": "web-dashboard/backend/logs"}, "features": {"strategy_management": true, "backtesting": true, "real_time_monitoring": true, "trading_management": true, "alert_system": true, "proxy_support": true}, "development": {"hot_reload": true, "debug_mode": true, "api_docs": "http://localhost:8081/api/docs"}}