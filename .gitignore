# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv_freqtrade/
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# OS
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
*~

# Logs
*.log
logs/
user_data/logs/
web-dashboard/backend/logs/
web-dashboard/backend/backend.log
web-dashboard/backend/backend.out
web-dashboard/frontend/frontend.log
web-dashboard/frontend/backend.log

# Database
*.db
*.sqlite
*.sqlite3
web-dashboard/backend/freqtrade_dashboard.db

# Freqtrade specific
user_data/data/
user_data/notebooks/
user_data/plot/
user_data/backtest_results/
user_data/hyperopt_results/
web-dashboard/backend/backtest_results/

# Config files with sensitive data
config/config.json
config/config_live.json
config/config_dry.json
config/config_real.json

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# React build
web-dashboard/frontend/build/
web-dashboard/frontend/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
.cache

# Process IDs
.backend.pid
.frontend.pid

# Jupyter
.ipynb_checkpoints/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Backup files
*.bak
*.backup

# Test files (keep only if needed)
test_*.py
*_test.py

# Scripts output
*.out
