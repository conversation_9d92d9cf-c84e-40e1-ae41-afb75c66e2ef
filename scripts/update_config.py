#!/usr/bin/env python3
"""配置更新脚本"""

import json
import os
from pathlib import Path

def load_env_file(env_path=".env"):
    """加载环境变量文件"""
    env_vars = {}
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    return env_vars

def update_freqtrade_config():
    """更新Freqtrade配置文件"""
    # 加载环境变量
    env_vars = load_env_file()
    
    # 检查必要的环境变量
    required_vars = ['BINANCE_API_KEY', 'BINANCE_SECRET_KEY']
    missing_vars = [var for var in required_vars if var not in env_vars]
    
    if missing_vars:
        print(f"警告: 缺少环境变量 {missing_vars}")
        print("将使用占位符配置（仅限模拟交易）")
    
    # 读取配置模板
    template_path = Path("config/config_template.json")
    config_path = Path("config/config.json")
    
    if not template_path.exists():
        print(f"错误: 配置模板文件不存在: {template_path}")
        return False
    
    with open(template_path, 'r') as f:
        config = json.load(f)
    
    # 更新配置
    config['exchange']['key'] = env_vars.get('BINANCE_API_KEY', '')
    config['exchange']['secret'] = env_vars.get('BINANCE_SECRET_KEY', '')
    
    # 如果有测试网配置，使用测试网
    if 'BINANCE_TESTNET_API_KEY' in env_vars and env_vars['BINANCE_TESTNET_API_KEY'] != 'your_testnet_api_key':
        config['exchange']['key'] = env_vars['BINANCE_TESTNET_API_KEY']
        config['exchange']['secret'] = env_vars['BINANCE_TESTNET_SECRET_KEY']
        config['exchange']['ccxt_config']['sandbox'] = True
        config['exchange']['ccxt_async_config']['sandbox'] = True
        print("使用测试网配置")
    
    # 更新Telegram配置
    if 'TELEGRAM_BOT_TOKEN' in env_vars and env_vars['TELEGRAM_BOT_TOKEN'] != 'your_telegram_bot_token':
        config['telegram']['enabled'] = True
        config['telegram']['token'] = env_vars['TELEGRAM_BOT_TOKEN']
        config['telegram']['chat_id'] = env_vars.get('TELEGRAM_CHAT_ID', '')
    
    # 保存配置
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    print(f"配置已更新: {config_path}")
    return True

def main():
    """主函数"""
    print("Optimus Python 配置更新")
    print("=" * 30)
    
    if update_freqtrade_config():
        print("✅ 配置更新完成！")
        print("\n下一步:")
        print("1. 设置你的币安API密钥到 .env 文件")
        print("2. 运行测试: python scripts/test_database.py")
        print("3. 启动Freqtrade: freqtrade trade --config config/config.json")
    else:
        print("❌ 配置更新失败")

if __name__ == "__main__":
    main()
