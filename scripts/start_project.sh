#!/bin/bash

# Freqtrade Web Dashboard 项目启动脚本
# 统一管理前后端服务的启动

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/web-dashboard/backend"
FRONTEND_DIR="$PROJECT_ROOT/web-dashboard/frontend"
VENV_PATH="$PROJECT_ROOT/venv_freqtrade"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查项目依赖..."
    
    # 检查freqtrade虚拟环境
    if [ ! -d "$VENV_PATH" ]; then
        print_message $RED "❌ 错误: freqtrade虚拟环境不存在: $VENV_PATH"
        print_message $YELLOW "请先运行: python -m venv venv_freqtrade && source venv_freqtrade/bin/activate && pip install freqtrade"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_message $RED "❌ 错误: Node.js 未安装"
        print_message $YELLOW "请先安装Node.js: https://nodejs.org/"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_message $RED "❌ 错误: npm 未安装"
        exit 1
    fi
    
    print_message $GREEN "✅ 依赖检查通过"
}

# 启动后端服务
start_backend() {
    print_message $BLUE "🚀 启动后端服务..."
    
    cd "$BACKEND_DIR"
    
    # 检查后端依赖
    if ! "$VENV_PATH/bin/python" -c "import freqtrade, fastapi, uvicorn" 2>/dev/null; then
        print_message $RED "❌ 错误: 后端依赖不完整"
        print_message $YELLOW "请在freqtrade虚拟环境中安装后端依赖: pip install -r requirements.txt"
        exit 1
    fi
    
    # 启动后端
    print_message $GREEN "✅ 后端服务启动中... (端口: 8081)"
    nohup "$VENV_PATH/bin/python" run.py > /dev/null 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > "$PROJECT_ROOT/.backend.pid"
    
    # 等待后端启动
    sleep 3
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_message $RED "❌ 后端服务启动失败"
        exit 1
    fi
}

# 启动前端服务
start_frontend() {
    print_message $BLUE "🎨 启动前端服务..."
    
    cd "$FRONTEND_DIR"
    
    # 检查前端依赖
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "📦 安装前端依赖..."
        npm install
    fi
    
    # 启动前端
    print_message $GREEN "✅ 前端服务启动中... (端口: 3000)"
    nohup npm start > /dev/null 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$PROJECT_ROOT/.frontend.pid"
}

# 停止服务
stop_services() {
    print_message $YELLOW "🛑 停止服务..."
    
    # 停止后端
    if [ -f "$PROJECT_ROOT/.backend.pid" ]; then
        BACKEND_PID=$(cat "$PROJECT_ROOT/.backend.pid")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_message $GREEN "✅ 后端服务已停止"
        fi
        rm -f "$PROJECT_ROOT/.backend.pid"
    fi
    
    # 停止前端
    if [ -f "$PROJECT_ROOT/.frontend.pid" ]; then
        FRONTEND_PID=$(cat "$PROJECT_ROOT/.frontend.pid")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_message $GREEN "✅ 前端服务已停止"
        fi
        rm -f "$PROJECT_ROOT/.frontend.pid"
    fi
}

# 显示帮助信息
show_help() {
    echo "Freqtrade Web Dashboard 项目启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动前后端服务"
    echo "  stop      停止前后端服务"
    echo "  restart   重启前后端服务"
    echo "  status    查看服务状态"
    echo "  help      显示此帮助信息"
    echo ""
}

# 查看服务状态
show_status() {
    print_message $BLUE "📊 服务状态:"
    
    # 检查后端状态
    if [ -f "$PROJECT_ROOT/.backend.pid" ]; then
        BACKEND_PID=$(cat "$PROJECT_ROOT/.backend.pid")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            print_message $GREEN "✅ 后端服务运行中 (PID: $BACKEND_PID, 端口: 8081)"
        else
            print_message $RED "❌ 后端服务未运行"
            rm -f "$PROJECT_ROOT/.backend.pid"
        fi
    else
        print_message $RED "❌ 后端服务未运行"
    fi
    
    # 检查前端状态
    if [ -f "$PROJECT_ROOT/.frontend.pid" ]; then
        FRONTEND_PID=$(cat "$PROJECT_ROOT/.frontend.pid")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            print_message $GREEN "✅ 前端服务运行中 (PID: $FRONTEND_PID, 端口: 3000)"
        else
            print_message $RED "❌ 前端服务未运行"
            rm -f "$PROJECT_ROOT/.frontend.pid"
        fi
    else
        print_message $RED "❌ 前端服务未运行"
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            check_dependencies
            start_backend
            start_frontend
            print_message $GREEN "🎉 项目启动完成!"
            print_message $BLUE "📱 前端地址: http://localhost:3000"
            print_message $BLUE "🔧 后端API: http://localhost:8081"
            print_message $BLUE "📚 API文档: http://localhost:8081/api/docs"
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            check_dependencies
            start_backend
            start_frontend
            print_message $GREEN "🔄 项目重启完成!"
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获Ctrl+C信号
trap 'stop_services; exit 0' INT

# 执行主函数
main "$@"
