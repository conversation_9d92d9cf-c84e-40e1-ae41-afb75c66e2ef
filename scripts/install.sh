#!/bin/bash

# Optimus Python 安装脚本

set -e

echo "🚀 开始安装 Optimus Python 量化交易系统"

# 检查Python版本
python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then 
    echo "✅ Python版本检查通过: $python_version"
else
    echo "❌ Python版本过低，需要3.9+，当前版本: $python_version"
    exit 1
fi

# 创建虚拟环境
echo "📦 创建虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 升级pip
echo "🔄 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📥 安装Python依赖..."
pip install -r requirements.txt

# 安装TA-Lib (如果系统支持)
echo "🔧 尝试安装TA-Lib..."
if command -v brew &> /dev/null; then
    echo "检测到Homebrew，安装TA-Lib..."
    brew install ta-lib
elif command -v apt-get &> /dev/null; then
    echo "检测到apt-get，安装TA-Lib..."
    sudo apt-get update
    sudo apt-get install -y build-essential libta-lib-dev
elif command -v yum &> /dev/null; then
    echo "检测到yum，安装TA-Lib..."
    sudo yum install -y gcc-c++ ta-lib-devel
else
    echo "⚠️  无法自动安装TA-Lib，请手动安装"
fi

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p user_data/{data,logs,notebooks,plots,strategies}
mkdir -p user_data/backtest_results

# 复制环境配置文件
if [ ! -f .env ]; then
    echo "📋 创建环境配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，填入您的API密钥和配置"
fi

# 检查配置文件
echo "🔍 检查配置文件..."
if [ ! -f config/config.json ]; then
    echo "❌ 配置文件缺失"
    exit 1
fi

# 运行基础测试
echo "🧪 运行安装测试..."
python -c "
import sys
try:
    from optimus.config import get_settings
    print('✅ 配置模块导入成功')
    
    from optimus.services import ArbitrageCalculator
    print('✅ 服务模块导入成功')
    
    from optimus.strategies import BaseArbitrageStrategy
    print('✅ 策略模块导入成功')
    
    print('🎉 Optimus Python 安装成功！')
    
except ImportError as e:
    print(f'❌ 导入失败: {e}')
    sys.exit(1)
"

echo ""
echo "🎯 安装完成！接下来的步骤："
echo "1. 编辑 .env 文件，配置您的API密钥"
echo "2. 运行: source venv/bin/activate"
echo "3. 测试: optimus config-info"
echo "4. 下载数据: optimus download-data --pairs \"BTC/USDT\" --days 7"
echo "5. 运行回测: optimus backtest --strategy Model3Strategy"
echo ""
echo "📚 更多信息请查看 README.md"