#!/bin/bash

# Optimus Python 快速启动脚本

set -e

echo "🚀 Optimus Python 快速启动"
echo "=========================================="

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    echo "📦 激活虚拟环境..."
    source venv/bin/activate
fi

# 设置Python路径
export PYTHONPATH="$PWD/src:$PYTHONPATH"

# 检查配置文件
echo "🔍 检查配置..."
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，从示例文件创建..."
    cp .env.example .env
    echo "❗ 请编辑 .env 文件并配置您的API密钥"
    echo "📝 编辑命令: nano .env"
    read -p "按回车键继续..."
fi

# 验证基础配置
echo "✅ 验证基础配置..."
python -c "
try:
    from optimus.config import get_settings
    settings = get_settings()
    print(f'环境: {settings.environment}')
    print(f'调试模式: {settings.debug}')
    if not settings.binance_api_key:
        print('⚠️  币安API密钥未配置')
    else:
        print('✅ API密钥已配置')
except Exception as e:
    print(f'❌ 配置验证失败: {e}')
    exit(1)
"

# 显示可用命令
echo ""
echo "📋 可用命令:"
echo "----------------------------------------"
echo "🔧 optimus config-info          - 查看配置信息"
echo "📊 optimus analyze-arbitrage    - 分析套利机会"
echo "📥 optimus download-data        - 下载历史数据" 
echo "📈 optimus backtest             - 运行策略回测"
echo "🚀 optimus run                  - 启动交易系统"
echo "📢 optimus test-notification    - 测试通知服务"
echo ""

# 询问用户要执行的操作
echo "请选择要执行的操作:"
echo "1) 查看配置信息"
echo "2) 下载示例数据"
echo "3) 运行示例回测"
echo "4) 分析套利机会"
echo "5) 测试通知服务"
echo "6) 启动交易系统"
echo "0) 退出"

read -p "请输入选项 (0-6): " choice

case $choice in
    1)
        echo "📋 查看配置信息..."
        python -m optimus.cli config-info
        ;;
    2)
        echo "📥 下载示例数据..."
        python -m optimus.cli download-data --pairs "BTC/USDT,ETH/USDT" --days 7
        ;;
    3)
        echo "📈 运行示例回测..."
        python -m optimus.cli backtest --strategy Model3Strategy --timerange 20241201-20241225
        ;;
    4)
        echo "📊 分析套利机会..."
        python -m optimus.cli analyze-arbitrage --symbol "BTC/USDT" --spot-price 95000 --futures-price 95100
        ;;
    5)
        echo "📢 测试通知服务..."
        python -m optimus.cli test-notification
        ;;
    6)
        echo "🚀 启动交易系统..."
        echo "⚠️  注意：这将启动实际的交易系统"
        read -p "确认启动? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            python -m optimus.cli run --dry-run true
        else
            echo "❌ 启动取消"
        fi
        ;;
    0)
        echo "👋 退出快速启动"
        ;;
    *)
        echo "❌ 无效选项"
        ;;
esac

echo ""
echo "🎯 快速启动完成！"
echo "📚 更多信息请查看 README.md 和 docs/ 目录"