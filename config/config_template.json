{"max_open_trades": 10, "stake_currency": "USDT", "stake_amount": 100, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true, "rateLimit": 1200}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 1200}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "XRP/USDT", "ADA/USDT", "LINK/USDT", "LTC/USDT", "DOT/USDT", "BNB/USDT", "BCH/USDT", "SOL/USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "optimus_secret_key", "ws_token": "optimus_ws_token", "CORS_origins": [], "username": "admin", "password": "password"}, "bot_name": "optimus-python", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}, "dataformat_ohlcv": "json", "dataformat_trades": "jsongz"}