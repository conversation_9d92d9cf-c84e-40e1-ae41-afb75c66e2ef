{"max_open_trades": 10, "stake_currency": "USDT", "stake_amount": 100, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "isolated", "strategy": "SimpleModel3Strategy", "timeframe": "5m", "stoploss": -0.05, "minimal_roi": {"0": 0.01, "30": 0.005, "60": 0.0025}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true, "rateLimit": 5000, "timeout": 30000, "retries": 5, "proxies": {"http": "http://127.0.0.1:7897", "https": "http://127.0.0.1:7897"}, "options": {"defaultType": "spot", "fetchMarkets": false}, "verify": false}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 5000, "timeout": 30000, "retries": 5, "proxies": {"http": "http://127.0.0.1:7897", "https": "http://127.0.0.1:7897"}, "options": {"defaultType": "spot", "fetchMarkets": false}, "verify": false}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "BNB/USDT", "XRP/USDT", "ADA/USDT", "SOL/USDT", "DOT/USDT", "DOGE/USDT", "AVAX/USDT", "MATIC/USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "dataformat_ohlcv": "json", "dataformat_trades": "json", "export": "trades", "cache": {"enabled": true, "max_download_retries": 5, "backoff_factor": 2.0}, "download_data": {"skip_exchanges_checks": true, "skip_pair_validation": true}}