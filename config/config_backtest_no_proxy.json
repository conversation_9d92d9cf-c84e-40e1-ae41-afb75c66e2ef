{"max_open_trades": 5, "stake_currency": "USDT", "stake_amount": 1000, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": false, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "timeframe": "5m", "strategy": "SimpleModel3Strategy", "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exchange": {"name": "binance", "ccxt_config": {"enableRateLimit": true, "rateLimit": 2000, "timeout": 120000, "retries": 10}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 2000, "timeout": 120000, "retries": 10}, "options": {"defaultType": "spot"}, "pair_whitelist": []}, "pairlists": [{"method": "StaticPairList"}], "datadir": "user_data/data", "internals": {"process_throttle_secs": 5, "python_venv": "venv_freqtrade_standalone"}}