{"max_open_trades": 10, "stake_currency": "USDT", "stake_amount": 100, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "isolated", "strategy": "SimpleModel3Strategy", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true, "rateLimit": 2000, "timeout": 120000, "proxies": {"http": "http://127.0.0.1:7897", "https": "http://127.0.0.1:7897"}, "retries": 10, "options": {"defaultType": "spot"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 2000, "timeout": 120000, "proxies": {"http": "http://127.0.0.1:7897", "https": "http://127.0.0.1:7897"}, "retries": 10, "options": {"defaultType": "spot"}}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "BNB/USDT", "XRP/USDT", "ADA/USDT", "SOL/USDT", "DOT/USDT", "DOGE/USDT", "AVAX/USDT", "MATIC/USDT"], "pair_blacklist": ["BNB/BTC", "BNB/ETH"]}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": true, "jwt_secret_key": "somethingrandom", "CORS_origins": [], "username": "freqtrader", "password": "password"}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}, "db_url": "sqlite:///tradesv3.sqlite"}